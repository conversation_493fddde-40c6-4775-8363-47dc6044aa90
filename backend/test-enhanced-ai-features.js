const mongoose = require('mongoose');
require('module-alias/register');
require('dotenv').config();

const enhancedAIService = require('./src/services/enhancedAIService');
const Equipment = require('./src/models/appModels/Equipment');
const ServiceOrder = require('./src/models/appModels/ServiceOrder');
const Opportunity = require('./src/models/appModels/Opportunity');
const Client = require('./src/models/appModels/Client');

async function connectDatabase() {
  try {
    const DATABASE_URL = process.env.DATABASE_URL || 'mongodb://localhost:27017/fulmark-hvac-crm';
    await mongoose.connect(DATABASE_URL);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Database connection error:', error);
    process.exit(1);
  }
}

async function createEnhancedTestData() {
  console.log('\n🔧 Creating enhanced test data...');

  try {
    // Create test client with more detailed data
    const testClient = await Client.create({
      name: 'Enhanced HVAC Solutions Sp. z o.o.',
      email: '<EMAIL>',
      phone: '+48987654321',
      address: 'ul. Nowoczesna 456, 00-001 Warszawa',
      buildingType: 'commercial',
      heatingSystem: 'Heat pump system',
      coolingSystem: 'VRV multi-split',
      serviceArea: 'Warszawa Centrum',
      contractType: 'maintenance_contract',
      priority: 'high',
      healthScore: 85,
      churnProbability: 0.15,
      lifetimeValue: 45000
    });

    console.log('✅ Created enhanced test client:', testClient.name);

    // Create multiple test equipment units
    const equipmentUnits = [];
    
    const equipmentData = [
      {
        name: 'Klimatyzator Daikin VRV-5',
        model: 'RXYQ10T8V1B',
        serialNumber: 'DK2024002',
        manufacturer: 'Daikin',
        type: 'air_conditioner',
        capacity: '28.0 kW',
        efficiency: 'A+++',
        refrigerantType: 'R-32',
        installationDate: new Date('2021-05-20'),
        warrantyExpiryDate: new Date('2026-05-20'),
        lastMaintenanceDate: new Date('2024-10-15'),
        nextMaintenanceDate: new Date('2025-04-15'),
        healthScore: 78,
        location: 'Biuro główne - parter'
      },
      {
        name: 'Pompa Ciepła LG Therma V',
        model: 'HU143.U33',
        serialNumber: 'LG2024001',
        manufacturer: 'LG',
        type: 'heat_pump',
        capacity: '14.0 kW',
        efficiency: 'A++',
        refrigerantType: 'R-410A',
        installationDate: new Date('2020-03-10'),
        warrantyExpiryDate: new Date('2025-03-10'),
        lastMaintenanceDate: new Date('2024-08-20'),
        nextMaintenanceDate: new Date('2025-02-20'),
        healthScore: 65,
        location: 'Sala konferencyjna - piętro 1'
      }
    ];

    for (const equipData of equipmentData) {
      const equipment = await Equipment.create({
        ...equipData,
        client: testClient._id
      });
      equipmentUnits.push(equipment);
      console.log('✅ Created enhanced test equipment:', equipment.name);
    }

    // Create test service orders with more history
    const serviceOrders = [];
    const serviceData = [
      {
        title: 'Kompleksowy przegląd systemu VRV',
        description: 'Szczegółowy przegląd systemu VRV z wymianą filtrów i czyszczeniem jednostek',
        orderNumber: 'SO-2024-ENH-001',
        stage: 'COMPLETED',
        priority: 'medium',
        type: 'maintenance',
        category: 'service',
        scheduledDate: new Date('2024-10-15'),
        completedDate: new Date('2024-10-15'),
        estimatedDuration: 6,
        actualDuration: 5.5,
        estimatedCost: 1200,
        actualCost: 1150,
        customerSatisfaction: 5,
        equipment: [equipmentUnits[0]._id]
      },
      {
        title: 'Naprawa pompy ciepła - wymiana czujnika',
        description: 'Wymiana uszkodzonego czujnika temperatury w pompie ciepła',
        orderNumber: 'SO-2024-ENH-002',
        stage: 'SCHEDULED',
        priority: 'high',
        type: 'repair',
        category: 'emergency',
        scheduledDate: new Date('2025-02-20'),
        estimatedDuration: 3,
        estimatedCost: 800,
        equipment: [equipmentUnits[1]._id]
      }
    ];

    for (const serviceData of serviceData) {
      const serviceOrder = await ServiceOrder.create({
        ...serviceData,
        client: testClient._id
      });
      serviceOrders.push(serviceOrder);
      console.log('✅ Created enhanced test service order:', serviceOrder.orderNumber);
    }

    // Create test opportunities with detailed data
    const opportunities = [];
    const opportunityData = [
      {
        name: 'Rozbudowa systemu klimatyzacji - nowe skrzydło',
        description: 'Instalacja systemu VRV w nowym skrzydle budynku biurowego',
        stage: 'PROPOSAL',
        value: 85000,
        probability: 75,
        serviceType: 'installation',
        equipmentType: 'air_conditioner',
        installationComplexity: 'complex',
        roomCount: 12,
        totalArea: 480,
        buildingType: 'commercial',
        leadSource: 'referral',
        leadQuality: 'hot',
        expectedCloseDate: new Date('2025-05-30'),
        competitors: ['Competitor A', 'Competitor B'],
        communicationHistory: [
          'Pierwsze spotkanie - prezentacja rozwiązania',
          'Wizyta techniczna - pomiary i wycena',
          'Przesłanie szczegółowej oferty'
        ]
      },
      {
        name: 'Modernizacja systemu grzewczego',
        description: 'Wymiana starego systemu grzewczego na nowoczesne pompy ciepła',
        stage: 'NEGOTIATION',
        value: 65000,
        probability: 60,
        serviceType: 'upgrade',
        equipmentType: 'heat_pump',
        installationComplexity: 'medium',
        roomCount: 8,
        totalArea: 320,
        buildingType: 'commercial',
        leadSource: 'website',
        leadQuality: 'warm',
        expectedCloseDate: new Date('2025-04-15')
      }
    ];

    for (const oppData of opportunityData) {
      const opportunity = await Opportunity.create({
        ...oppData,
        client: testClient._id
      });
      opportunities.push(opportunity);
      console.log('✅ Created enhanced test opportunity:', opportunity.name);
    }

    return {
      client: testClient,
      equipment: equipmentUnits,
      serviceOrders: serviceOrders,
      opportunities: opportunities
    };

  } catch (error) {
    console.error('❌ Error creating enhanced test data:', error);
    throw error;
  }
}

async function testAdvancedEquipmentDiagnostics(equipmentId) {
  console.log('\n🤖 Testing Advanced Equipment Diagnostics with Function Calling...');

  try {
    const result = await enhancedAIService.performAdvancedEquipmentDiagnostics(equipmentId);
    
    console.log('✅ Advanced Equipment Diagnostics Results:');
    console.log(`   Diagnostics: ${result.diagnostics}`);
    console.log(`   Tool Calls: ${JSON.stringify(result.toolCalls, null, 2)}`);
    console.log(`   Confidence: ${(result.confidence * 100).toFixed(1)}%`);
    console.log(`   Analysis Date: ${result.timestamp.toLocaleString('pl-PL')}`);

    return result;
  } catch (error) {
    console.error('❌ Advanced Equipment Diagnostics test failed:', error);
    throw error;
  }
}

async function testIntelligentLeadScoring(opportunityId) {
  console.log('\n🎯 Testing Intelligent Lead Scoring with Structured Output...');

  try {
    const result = await enhancedAIService.performIntelligentLeadScoring(opportunityId);
    
    console.log('✅ Intelligent Lead Scoring Results:');
    console.log(`   Lead Score: ${result.leadScore}/100`);
    console.log(`   Conversion Probability: ${(result.conversionProbability * 100).toFixed(1)}%`);
    console.log(`   Priority: ${result.priority}`);
    console.log(`   Insights: ${result.insights}`);
    console.log('   Recommendations:');
    result.recommendations.forEach(rec => {
      console.log(`     - ${rec}`);
    });
    console.log('   Next Actions:');
    result.nextActions.forEach(action => {
      console.log(`     - ${action}`);
    });

    return result;
  } catch (error) {
    console.error('❌ Intelligent Lead Scoring test failed:', error);
    throw error;
  }
}

async function testComprehensiveCustomerAnalysis(clientId) {
  console.log('\n👤 Testing Comprehensive Customer Analysis...');

  try {
    const result = await enhancedAIService.performComprehensiveCustomerAnalysis(clientId);
    
    console.log('✅ Comprehensive Customer Analysis Results:');
    console.log(`   Health Score: ${result.healthScore}/100`);
    console.log(`   Churn Probability: ${(result.churnProbability * 100).toFixed(1)}%`);
    console.log(`   Lifetime Value: ${result.lifetimeValue.toLocaleString('pl-PL')} PLN`);
    console.log(`   Segment: ${result.segment}`);
    console.log(`   Insights: ${result.insights}`);
    console.log('   Recommendations:');
    result.recommendations.forEach(rec => {
      console.log(`     - ${rec}`);
    });
    console.log('   Upsell Opportunities:');
    result.upsellOpportunities.forEach(opp => {
      console.log(`     - ${opp}`);
    });

    return result;
  } catch (error) {
    console.error('❌ Comprehensive Customer Analysis test failed:', error);
    throw error;
  }
}

async function testPredictiveMaintenanceSchedule() {
  console.log('\n📅 Testing Predictive Maintenance Scheduler...');

  try {
    const result = await enhancedAIService.generatePredictiveMaintenanceSchedule();
    
    console.log('✅ Predictive Maintenance Schedule Results:');
    console.log(`   Schedule: ${result.schedule}`);
    console.log(`   Optimizations: ${JSON.stringify(result.optimizations, null, 2)}`);
    console.log(`   Equipment Count: ${result.equipmentCount}`);
    console.log(`   Generated At: ${result.generatedAt.toLocaleString('pl-PL')}`);

    return result;
  } catch (error) {
    console.error('❌ Predictive Maintenance Schedule test failed:', error);
    throw error;
  }
}

async function testStreamingInsights() {
  console.log('\n🌊 Testing Streaming AI Insights...');

  try {
    console.log('✅ Starting streaming test...');
    
    let messageCount = 0;
    const maxMessages = 5;
    
    for await (const insight of enhancedAIService.streamDashboardInsights()) {
      console.log(`   Stream Message ${messageCount + 1}: ${insight.content}`);
      messageCount++;
      
      if (messageCount >= maxMessages) {
        console.log('✅ Streaming test completed (limited to 5 messages)');
        break;
      }
    }

    return { messageCount, status: 'completed' };
  } catch (error) {
    console.error('❌ Streaming Insights test failed:', error);
    throw error;
  }
}

async function cleanupEnhancedTestData() {
  console.log('\n🧹 Cleaning up enhanced test data...');

  try {
    await ServiceOrder.deleteMany({ orderNumber: /ENH/ });
    await Opportunity.deleteMany({ name: /Rozbudowa systemu klimatyzacji|Modernizacja systemu/ });
    await Equipment.deleteMany({ serialNumber: /DK2024002|LG2024001/ });
    await Client.deleteMany({ name: /Enhanced HVAC Solutions/ });
    
    console.log('✅ Enhanced test data cleaned up');
  } catch (error) {
    console.error('❌ Error cleaning up enhanced test data:', error);
  }
}

async function runEnhancedAITests() {
  console.log('🚀 Starting Enhanced AI Features Test Suite');
  console.log('==========================================');
  console.log('🔥 Testing OpenAI Function Calling, Structured Output & Streaming');
  console.log('==========================================');

  try {
    // Connect to database
    await connectDatabase();

    // Clean up any existing test data
    await cleanupEnhancedTestData();

    // Create enhanced test data
    const testData = await createEnhancedTestData();

    // Test enhanced AI features
    await testAdvancedEquipmentDiagnostics(testData.equipment[0]._id);
    await testIntelligentLeadScoring(testData.opportunities[0]._id);
    await testComprehensiveCustomerAnalysis(testData.client._id);
    await testPredictiveMaintenanceSchedule();
    await testStreamingInsights();

    console.log('\n🎉 All Enhanced AI tests completed successfully!');
    console.log('==========================================');
    console.log('✅ Advanced Equipment Diagnostics: Working');
    console.log('✅ Intelligent Lead Scoring: Working');
    console.log('✅ Comprehensive Customer Analysis: Working');
    console.log('✅ Predictive Maintenance Scheduler: Working');
    console.log('✅ Streaming AI Insights: Working');
    console.log('\n🚀 Your Enhanced AI-powered HVAC CRM is ready for production!');
    console.log('🏆 You now have the most advanced HVAC CRM in Europe!');

    // Clean up test data
    await cleanupEnhancedTestData();

  } catch (error) {
    console.error('\n❌ Enhanced AI Test Suite Failed:', error);
    console.log('\n🔧 Troubleshooting Tips:');
    console.log('1. Make sure MongoDB is running');
    console.log('2. Check your .env file configuration');
    console.log('3. Verify OpenAI API key is set: OPENAI_API_KEY=your-key');
    console.log('4. Ensure all dependencies are installed: npm install');
    console.log('5. Check that zod and zod-to-json-schema are installed');
  } finally {
    // Close database connection
    await mongoose.connection.close();
    console.log('\n📝 Database connection closed');
    process.exit(0);
  }
}

// Run the enhanced test suite
if (require.main === module) {
  runEnhancedAITests();
}

module.exports = {
  runEnhancedAITests,
  testAdvancedEquipmentDiagnostics,
  testIntelligentLeadScoring,
  testComprehensiveCustomerAnalysis,
  testPredictiveMaintenanceSchedule,
  testStreamingInsights
};
