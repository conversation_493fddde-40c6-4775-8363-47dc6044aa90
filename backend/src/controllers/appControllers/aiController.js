const aiService = require('@/services/aiService');
const { catchErrors } = require('@/handlers/errorHandlers');

const aiController = {
  /**
   * PREDICTIVE MAINTENANCE ENDPOINT
   * POST /api/ai/predictive-maintenance/:equipmentId
   */
  predictiveMaintenance: catchErrors(async (req, res) => {
    const { equipmentId } = req.params;
    
    try {
      const result = await aiService.predictEquipmentMaintenance(equipmentId);
      
      res.status(200).json({
        success: true,
        result: {
          equipmentId,
          healthScore: result.healthScore,
          failureProbability: result.failureProbability,
          recommendations: result.recommendations,
          insights: result.insights,
          analysisDate: new Date()
        },
        message: 'Analiza predykcyjna zakończona pomyślnie'
      });
    } catch (error) {
      console.error('Predictive maintenance error:', error);
      res.status(500).json({
        success: false,
        message: 'Błąd podczas analizy predykcyjnej',
        error: error.message
      });
    }
  }),

  /**
   * LEAD SCORING ENDPOINT
   * POST /api/ai/lead-scoring/:opportunityId
   */
  leadScoring: catchErrors(async (req, res) => {
    const { opportunityId } = req.params;
    
    try {
      const result = await aiService.calculateLeadScore(opportunityId);
      
      res.status(200).json({
        success: true,
        result: {
          opportunityId,
          leadScore: result.leadScore,
          factors: result.factors,
          insights: result.insights,
          analysisDate: new Date()
        },
        message: 'Ocena leada zakończona pomyślnie'
      });
    } catch (error) {
      console.error('Lead scoring error:', error);
      res.status(500).json({
        success: false,
        message: 'Błąd podczas oceny leada',
        error: error.message
      });
    }
  }),

  /**
   * CUSTOMER INTELLIGENCE ENDPOINT
   * POST /api/ai/customer-intelligence/:clientId
   */
  customerIntelligence: catchErrors(async (req, res) => {
    const { clientId } = req.params;
    
    try {
      const result = await aiService.analyzeCustomerIntelligence(clientId);
      
      res.status(200).json({
        success: true,
        result: {
          clientId,
          healthScore: result.healthScore,
          churnProbability: result.churnProbability,
          lifetimeValue: result.lifetimeValue,
          insights: result.insights,
          analysisDate: new Date()
        },
        message: 'Analiza klienta zakończona pomyślnie'
      });
    } catch (error) {
      console.error('Customer intelligence error:', error);
      res.status(500).json({
        success: false,
        message: 'Błąd podczas analizy klienta',
        error: error.message
      });
    }
  }),

  /**
   * BULK EQUIPMENT ANALYSIS
   * POST /api/ai/bulk-equipment-analysis
   */
  bulkEquipmentAnalysis: catchErrors(async (req, res) => {
    const { equipmentIds } = req.body;
    
    if (!equipmentIds || !Array.isArray(equipmentIds)) {
      return res.status(400).json({
        success: false,
        message: 'Wymagana jest lista ID sprzętu'
      });
    }

    try {
      const results = [];
      
      for (const equipmentId of equipmentIds) {
        try {
          const result = await aiService.predictEquipmentMaintenance(equipmentId);
          results.push({
            equipmentId,
            success: true,
            ...result
          });
        } catch (error) {
          results.push({
            equipmentId,
            success: false,
            error: error.message
          });
        }
      }
      
      res.status(200).json({
        success: true,
        result: {
          totalAnalyzed: equipmentIds.length,
          successful: results.filter(r => r.success).length,
          failed: results.filter(r => !r.success).length,
          results,
          analysisDate: new Date()
        },
        message: `Analiza ${equipmentIds.length} urządzeń zakończona`
      });
    } catch (error) {
      console.error('Bulk equipment analysis error:', error);
      res.status(500).json({
        success: false,
        message: 'Błąd podczas masowej analizy sprzętu',
        error: error.message
      });
    }
  }),

  /**
   * BULK LEAD SCORING
   * POST /api/ai/bulk-lead-scoring
   */
  bulkLeadScoring: catchErrors(async (req, res) => {
    const { opportunityIds } = req.body;
    
    if (!opportunityIds || !Array.isArray(opportunityIds)) {
      return res.status(400).json({
        success: false,
        message: 'Wymagana jest lista ID okazji'
      });
    }

    try {
      const results = [];
      
      for (const opportunityId of opportunityIds) {
        try {
          const result = await aiService.calculateLeadScore(opportunityId);
          results.push({
            opportunityId,
            success: true,
            ...result
          });
        } catch (error) {
          results.push({
            opportunityId,
            success: false,
            error: error.message
          });
        }
      }
      
      res.status(200).json({
        success: true,
        result: {
          totalAnalyzed: opportunityIds.length,
          successful: results.filter(r => r.success).length,
          failed: results.filter(r => !r.success).length,
          results,
          analysisDate: new Date()
        },
        message: `Ocena ${opportunityIds.length} leadów zakończona`
      });
    } catch (error) {
      console.error('Bulk lead scoring error:', error);
      res.status(500).json({
        success: false,
        message: 'Błąd podczas masowej oceny leadów',
        error: error.message
      });
    }
  }),

  /**
   * AI DASHBOARD INSIGHTS
   * GET /api/ai/dashboard-insights
   */
  dashboardInsights: catchErrors(async (req, res) => {
    try {
      // Get AI-powered insights for dashboard
      const Equipment = require('@/models/appModels/Equipment');
      const Opportunity = require('@/models/appModels/Opportunity');
      const Client = require('@/models/appModels/Client');

      // Equipment insights
      const criticalEquipment = await Equipment.find({
        healthScore: { $lt: 30 }
      }).populate('client').limit(10);

      const maintenanceDue = await Equipment.find({
        nextMaintenanceDate: { $lt: new Date() }
      }).populate('client').limit(10);

      // Lead insights
      const hotLeads = await Opportunity.find({
        leadScore: { $gte: 80 },
        stage: { $in: ['NEW_LEAD', 'QUALIFIED', 'PROPOSAL'] }
      }).populate('client').limit(10);

      const churnRiskClients = await Client.find({
        churnProbability: { $gte: 0.5 }
      }).limit(10);

      // Calculate summary metrics
      const totalCriticalEquipment = await Equipment.countDocuments({
        healthScore: { $lt: 30 }
      });

      const totalMaintenanceDue = await Equipment.countDocuments({
        nextMaintenanceDate: { $lt: new Date() }
      });

      const totalHotLeads = await Opportunity.countDocuments({
        leadScore: { $gte: 80 },
        stage: { $in: ['NEW_LEAD', 'QUALIFIED', 'PROPOSAL'] }
      });

      const totalChurnRisk = await Client.countDocuments({
        churnProbability: { $gte: 0.5 }
      });

      res.status(200).json({
        success: true,
        result: {
          summary: {
            criticalEquipment: totalCriticalEquipment,
            maintenanceDue: totalMaintenanceDue,
            hotLeads: totalHotLeads,
            churnRiskClients: totalChurnRisk
          },
          details: {
            criticalEquipment,
            maintenanceDue,
            hotLeads,
            churnRiskClients
          },
          lastUpdated: new Date()
        },
        message: 'Insights AI wygenerowane pomyślnie'
      });
    } catch (error) {
      console.error('Dashboard insights error:', error);
      res.status(500).json({
        success: false,
        message: 'Błąd podczas generowania insights AI',
        error: error.message
      });
    }
  }),

  /**
   * AI ANALYTICS SUMMARY
   * GET /api/ai/analytics-summary
   */
  analyticsSummary: catchErrors(async (req, res) => {
    try {
      const Equipment = require('@/models/appModels/Equipment');
      const Opportunity = require('@/models/appModels/Opportunity');
      const Client = require('@/models/appModels/Client');

      // Equipment analytics
      const equipmentStats = await Equipment.aggregate([
        {
          $group: {
            _id: null,
            avgHealthScore: { $avg: '$healthScore' },
            totalEquipment: { $sum: 1 },
            criticalCount: {
              $sum: { $cond: [{ $lt: ['$healthScore', 30] }, 1, 0] }
            },
            healthyCount: {
              $sum: { $cond: [{ $gte: ['$healthScore', 80] }, 1, 0] }
            }
          }
        }
      ]);

      // Lead scoring analytics
      const leadStats = await Opportunity.aggregate([
        {
          $group: {
            _id: null,
            avgLeadScore: { $avg: '$leadScore' },
            totalOpportunities: { $sum: 1 },
            hotLeads: {
              $sum: { $cond: [{ $gte: ['$leadScore', 80] }, 1, 0] }
            },
            coldLeads: {
              $sum: { $cond: [{ $lt: ['$leadScore', 40] }, 1, 0] }
            }
          }
        }
      ]);

      // Customer analytics
      const customerStats = await Client.aggregate([
        {
          $group: {
            _id: null,
            avgHealthScore: { $avg: '$healthScore' },
            avgChurnProbability: { $avg: '$churnProbability' },
            totalClients: { $sum: 1 },
            healthyClients: {
              $sum: { $cond: [{ $gte: ['$healthScore', 80] }, 1, 0] }
            },
            riskClients: {
              $sum: { $cond: [{ $gte: ['$churnProbability', 0.5] }, 1, 0] }
            }
          }
        }
      ]);

      res.status(200).json({
        success: true,
        result: {
          equipment: equipmentStats[0] || {},
          leads: leadStats[0] || {},
          customers: customerStats[0] || {},
          generatedAt: new Date()
        },
        message: 'Podsumowanie analityki AI wygenerowane pomyślnie'
      });
    } catch (error) {
      console.error('Analytics summary error:', error);
      res.status(500).json({
        success: false,
        message: 'Błąd podczas generowania podsumowania analityki',
        error: error.message
      });
    }
  })
};

module.exports = aiController;
