const OpenAI = require('openai');
const { Equipment } = require('@/models/appModels/Equipment');
const { ServiceOrder } = require('@/models/appModels/ServiceOrder');
const { Opportunity } = require('@/models/appModels/Opportunity');
const { Client } = require('@/models/appModels/Client');

class AIService {
  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
  }

  /**
   * PREDICTIVE MAINTENANCE ENGINE
   * Analyzes equipment data to predict maintenance needs and failures
   */
  async predictEquipmentMaintenance(equipmentId) {
    try {
      const equipment = await Equipment.findById(equipmentId).populate('client');
      if (!equipment) throw new Error('Equipment not found');

      // Get historical service data
      const serviceHistory = await ServiceOrder.find({ 
        equipment: equipmentId 
      }).sort({ created: -1 }).limit(10);

      // Calculate health score based on multiple factors
      const healthScore = this.calculateHealthScore(equipment, serviceHistory);
      
      // Predict failure probability
      const failureProbability = this.calculateFailureProbability(equipment, serviceHistory);
      
      // Generate maintenance recommendations
      const recommendations = await this.generateMaintenanceRecommendations(
        equipment, 
        serviceHistory, 
        healthScore
      );

      // Update equipment with AI insights
      await Equipment.findByIdAndUpdate(equipmentId, {
        healthScore,
        failureProbability,
        maintenancePredictions: {
          nextMaintenanceDate: recommendations.nextMaintenanceDate,
          urgency: recommendations.urgency,
          estimatedCost: recommendations.estimatedCost,
          recommendedActions: recommendations.actions,
          lastAnalysis: new Date()
        }
      });

      return {
        healthScore,
        failureProbability,
        recommendations,
        insights: recommendations.insights
      };

    } catch (error) {
      console.error('Predictive maintenance error:', error);
      throw error;
    }
  }

  /**
   * INTELLIGENT LEAD SCORING
   * Analyzes opportunities to calculate lead scores and conversion probability
   */
  async calculateLeadScore(opportunityId) {
    try {
      const opportunity = await Opportunity.findById(opportunityId).populate('client');
      if (!opportunity) throw new Error('Opportunity not found');

      const client = opportunity.client;
      
      // Scoring factors
      const factors = {
        // Client factors (40% weight)
        clientSize: this.scoreClientSize(client),
        clientHistory: await this.scoreClientHistory(client._id),
        clientHealth: client.healthScore || 50,
        
        // Opportunity factors (35% weight)
        opportunityValue: this.scoreOpportunityValue(opportunity.value),
        serviceType: this.scoreServiceType(opportunity.serviceType),
        complexity: this.scoreComplexity(opportunity.installationComplexity),
        
        // Market factors (25% weight)
        leadSource: this.scoreLeadSource(opportunity.leadSource),
        seasonality: this.scoreSeasonality(),
        competition: this.scoreCompetition(opportunity.competitors?.length || 0)
      };

      // Calculate weighted score
      const leadScore = Math.round(
        (factors.clientSize * 0.15) +
        (factors.clientHistory * 0.15) +
        (factors.clientHealth * 0.10) +
        (factors.opportunityValue * 0.15) +
        (factors.serviceType * 0.10) +
        (factors.complexity * 0.10) +
        (factors.leadSource * 0.10) +
        (factors.seasonality * 0.08) +
        (factors.competition * 0.07)
      );

      // Generate AI insights
      const insights = await this.generateLeadInsights(opportunity, factors, leadScore);

      // Update opportunity with AI data
      await Opportunity.findByIdAndUpdate(opportunityId, {
        leadScore,
        aiInsights: {
          factors,
          insights,
          lastAnalysis: new Date(),
          recommendations: insights.recommendations
        }
      });

      return {
        leadScore,
        factors,
        insights
      };

    } catch (error) {
      console.error('Lead scoring error:', error);
      throw error;
    }
  }

  /**
   * CUSTOMER INTELLIGENCE PLATFORM
   * Analyzes customer data for health score, churn risk, and upselling opportunities
   */
  async analyzeCustomerIntelligence(clientId) {
    try {
      const client = await Client.findById(clientId);
      if (!client) throw new Error('Client not found');

      // Get customer data
      const equipment = await Equipment.find({ client: clientId });
      const serviceOrders = await ServiceOrder.find({ client: clientId }).sort({ created: -1 });
      const opportunities = await Opportunity.find({ client: clientId }).sort({ created: -1 });

      // Calculate metrics
      const healthScore = this.calculateCustomerHealthScore(client, equipment, serviceOrders);
      const churnProbability = this.calculateChurnProbability(client, serviceOrders);
      const lifetimeValue = this.calculateLifetimeValue(serviceOrders, opportunities);
      
      // Generate insights
      const insights = await this.generateCustomerInsights(
        client, 
        equipment, 
        serviceOrders, 
        opportunities,
        { healthScore, churnProbability, lifetimeValue }
      );

      // Update client with AI insights
      await Client.findByIdAndUpdate(clientId, {
        healthScore,
        churnProbability,
        lifetimeValue,
        aiInsights: {
          insights,
          lastAnalysis: new Date(),
          recommendations: insights.recommendations,
          riskFactors: insights.riskFactors,
          opportunities: insights.upsellOpportunities
        }
      });

      return {
        healthScore,
        churnProbability,
        lifetimeValue,
        insights
      };

    } catch (error) {
      console.error('Customer intelligence error:', error);
      throw error;
    }
  }

  // HELPER METHODS FOR CALCULATIONS

  calculateHealthScore(equipment, serviceHistory) {
    let score = 100;
    
    // Age factor
    const ageInYears = equipment.installationDate ? 
      (Date.now() - equipment.installationDate.getTime()) / (1000 * 60 * 60 * 24 * 365) : 0;
    score -= Math.min(ageInYears * 5, 30); // Max 30 points for age
    
    // Service frequency factor
    const recentServices = serviceHistory.filter(s => 
      s.created > new Date(Date.now() - 365 * 24 * 60 * 60 * 1000)
    );
    if (recentServices.length > 3) score -= 20; // Frequent repairs
    
    // Maintenance compliance
    if (equipment.nextMaintenanceDate && equipment.nextMaintenanceDate < new Date()) {
      score -= 15; // Overdue maintenance
    }
    
    return Math.max(Math.round(score), 0);
  }

  calculateFailureProbability(equipment, serviceHistory) {
    let probability = 0;
    
    // Age factor
    const ageInYears = equipment.installationDate ? 
      (Date.now() - equipment.installationDate.getTime()) / (1000 * 60 * 60 * 24 * 365) : 0;
    probability += Math.min(ageInYears * 0.05, 0.3);
    
    // Service history factor
    const emergencyServices = serviceHistory.filter(s => s.type === 'emergency').length;
    probability += emergencyServices * 0.1;
    
    // Health score factor
    const healthScore = equipment.healthScore || 100;
    probability += (100 - healthScore) * 0.005;
    
    return Math.min(Math.round(probability * 100) / 100, 1);
  }

  async generateMaintenanceRecommendations(equipment, serviceHistory, healthScore) {
    const recommendations = {
      urgency: 'low',
      estimatedCost: 0,
      actions: [],
      insights: [],
      nextMaintenanceDate: null
    };

    // Determine urgency based on health score
    if (healthScore < 30) {
      recommendations.urgency = 'critical';
      recommendations.estimatedCost = 1500;
      recommendations.actions.push('Immediate inspection required');
    } else if (healthScore < 60) {
      recommendations.urgency = 'high';
      recommendations.estimatedCost = 800;
      recommendations.actions.push('Schedule maintenance within 2 weeks');
    } else if (healthScore < 80) {
      recommendations.urgency = 'medium';
      recommendations.estimatedCost = 400;
      recommendations.actions.push('Schedule routine maintenance');
    }

    // Calculate next maintenance date
    const lastMaintenance = serviceHistory.find(s => s.type === 'maintenance');
    const baseDate = lastMaintenance ? lastMaintenance.created : equipment.installationDate;
    const monthsToAdd = equipment.type === 'air_conditioner' ? 6 : 12;
    
    recommendations.nextMaintenanceDate = new Date(baseDate);
    recommendations.nextMaintenanceDate.setMonth(recommendations.nextMaintenanceDate.getMonth() + monthsToAdd);

    return recommendations;
  }

  scoreClientSize(client) {
    // Score based on building type and estimated size
    const sizeScores = {
      'residential': 30,
      'office': 50,
      'commercial': 70,
      'industrial': 85,
      'warehouse': 90,
      'retail': 60
    };
    return sizeScores[client.buildingType] || 40;
  }

  async scoreClientHistory(clientId) {
    const serviceOrders = await ServiceOrder.find({ client: clientId });
    const completedOrders = serviceOrders.filter(s => s.stage === 'COMPLETED');
    
    if (serviceOrders.length === 0) return 30; // New client
    
    const completionRate = completedOrders.length / serviceOrders.length;
    const avgSatisfaction = completedOrders.reduce((sum, order) => 
      sum + (order.customerSatisfaction || 3), 0) / completedOrders.length;
    
    return Math.round((completionRate * 50) + (avgSatisfaction * 10));
  }

  scoreOpportunityValue(value) {
    if (value < 1000) return 20;
    if (value < 5000) return 40;
    if (value < 15000) return 60;
    if (value < 50000) return 80;
    return 100;
  }

  scoreServiceType(serviceType) {
    const typeScores = {
      'installation': 90,
      'maintenance_contract': 85,
      'upgrade': 75,
      'repair': 40,
      'consultation': 30
    };
    return typeScores[serviceType] || 50;
  }

  scoreComplexity(complexity) {
    const complexityScores = {
      'simple': 90,
      'medium': 70,
      'complex': 50,
      'very_complex': 30
    };
    return complexityScores[complexity] || 60;
  }

  scoreLeadSource(leadSource) {
    const sourceScores = {
      'referral': 95,
      'website': 80,
      'social_media': 60,
      'advertising': 70,
      'trade_show': 85,
      'cold_call': 30,
      'other': 40
    };
    return sourceScores[leadSource] || 50;
  }

  scoreSeasonality() {
    const month = new Date().getMonth();
    // HVAC seasonal demand (Poland)
    const seasonalScores = [40, 45, 60, 75, 85, 95, 100, 95, 80, 65, 50, 35];
    return seasonalScores[month];
  }

  scoreCompetition(competitorCount) {
    if (competitorCount === 0) return 100;
    if (competitorCount === 1) return 80;
    if (competitorCount === 2) return 60;
    if (competitorCount >= 3) return 40;
    return 70;
  }

  async generateLeadInsights(opportunity, factors, leadScore) {
    const insights = {
      summary: '',
      recommendations: [],
      riskFactors: [],
      strengths: []
    };

    // Generate summary based on score
    if (leadScore >= 80) {
      insights.summary = 'Wysokiej jakości lead z dużym potencjałem konwersji';
      insights.recommendations.push('Priorytetowe traktowanie - szybki kontakt');
      insights.recommendations.push('Przygotowanie szczegółowej oferty');
    } else if (leadScore >= 60) {
      insights.summary = 'Średniej jakości lead wymagający nurturingu';
      insights.recommendations.push('Regularne follow-up');
      insights.recommendations.push('Edukacja o korzyściach');
    } else {
      insights.summary = 'Lead niskiej jakości - ostrożne podejście';
      insights.recommendations.push('Kwalifikacja dodatkowa');
      insights.recommendations.push('Minimalne zasoby');
    }

    return insights;
  }

  calculateCustomerHealthScore(client, equipment, serviceOrders) {
    let score = 50; // Base score
    
    // Equipment factor
    const avgEquipmentHealth = equipment.reduce((sum, eq) => sum + (eq.healthScore || 50), 0) / equipment.length;
    score += (avgEquipmentHealth - 50) * 0.3;
    
    // Service satisfaction
    const recentOrders = serviceOrders.slice(0, 5);
    const avgSatisfaction = recentOrders.reduce((sum, order) => 
      sum + (order.customerSatisfaction || 3), 0) / recentOrders.length;
    score += (avgSatisfaction - 3) * 10;
    
    // Payment history (simplified)
    const completedOrders = serviceOrders.filter(s => s.stage === 'COMPLETED');
    if (completedOrders.length > 0) {
      score += 20; // Good payment history
    }
    
    return Math.max(Math.min(Math.round(score), 100), 0);
  }

  calculateChurnProbability(client, serviceOrders) {
    let probability = 0.1; // Base probability
    
    // Time since last service
    const lastService = serviceOrders[0];
    if (lastService) {
      const daysSinceLastService = (Date.now() - lastService.created.getTime()) / (1000 * 60 * 60 * 24);
      if (daysSinceLastService > 365) probability += 0.3;
      else if (daysSinceLastService > 180) probability += 0.1;
    }
    
    // Service satisfaction
    const recentOrders = serviceOrders.slice(0, 3);
    const avgSatisfaction = recentOrders.reduce((sum, order) => 
      sum + (order.customerSatisfaction || 3), 0) / recentOrders.length;
    if (avgSatisfaction < 3) probability += 0.2;
    
    return Math.min(Math.round(probability * 100) / 100, 1);
  }

  calculateLifetimeValue(serviceOrders, opportunities) {
    const serviceValue = serviceOrders.reduce((sum, order) => sum + (order.actualCost || 0), 0);
    const opportunityValue = opportunities
      .filter(opp => opp.stage === 'CLOSED_WON')
      .reduce((sum, opp) => sum + (opp.value || 0), 0);
    
    return serviceValue + opportunityValue;
  }

  async generateCustomerInsights(client, equipment, serviceOrders, opportunities, metrics) {
    const insights = {
      summary: '',
      recommendations: [],
      riskFactors: [],
      upsellOpportunities: []
    };

    // Health-based insights
    if (metrics.healthScore >= 80) {
      insights.summary = 'Zdrowy klient o wysokiej wartości';
      insights.recommendations.push('Utrzymanie wysokiej jakości obsługi');
    } else if (metrics.healthScore >= 60) {
      insights.summary = 'Klient wymagający uwagi';
      insights.recommendations.push('Proaktywny kontakt');
    } else {
      insights.summary = 'Klient wysokiego ryzyka';
      insights.riskFactors.push('Niska satysfakcja z usług');
      insights.recommendations.push('Natychmiastowa interwencja');
    }

    // Churn risk factors
    if (metrics.churnProbability > 0.5) {
      insights.riskFactors.push('Wysokie ryzyko utraty klienta');
      insights.recommendations.push('Program retencji');
    }

    // Upselling opportunities
    if (equipment.length > 0 && metrics.lifetimeValue > 5000) {
      insights.upsellOpportunities.push('Rozszerzenie systemu HVAC');
      insights.upsellOpportunities.push('Kontrakt serwisowy');
    }

    return insights;
  }
}

module.exports = new AIService();
