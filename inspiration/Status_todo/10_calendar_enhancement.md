# Calendar Enhancement for Multiple Service Order Types

## Overview

This document outlines the implementation of enhanced calendar functionality in the HVAC CRM Servicetool to support three types of service orders:

1. **Service** - Regular maintenance and repair services
2. **Installation** - New equipment installations
3. **Inspection** - Site inspections and evaluations

The implementation includes visual differentiation in the calendar view, filtering capabilities, and proper data modeling.

## Implementation Details

### 1. Database Schema Update

Added a `type` field to the `ServiceOrder` model in the Prisma schema:

```prisma
model ServiceOrder {
  id          String @id @default(cuid())
  title       String
  description String?
  status      String @default("PENDING") // PENDING, IN_PROGRESS, COMPLETED, CANCELLED
  priority    String @default("MEDIUM") // LOW, MEDIUM, HIGH, URGENT
  type        String @default("SERVICE") // SERVICE, INSTALLATION, INSPECTION
  scheduledDate DateTime?
  completedDate DateTime?
  notes       String?
  // ... other fields
}
```

### 2. Service Layer Updates

Updated the `service-order.service.ts` file to support filtering by type:

- Added `type` to the `FilterParams` interface
- Updated the `getServiceOrders` function to handle type filtering
- Updated the `createServiceOrder` and `updateServiceOrder` functions to handle the type field

### 3. UI Enhancements

#### Service Order Forms

- Added type selection to the service order creation form
- Added type selection to the service order edit form
- Added validation for the type field

#### Service Order List

- Added type filtering to the service order list page
- Added type badges to service order cards
- Added helper function to get type-specific colors

#### Calendar View

- Updated the calendar to display different colors based on service order type
- Added a legend to explain the different colors
- Enhanced the event display to show the service order type

### 4. Visual Differentiation

Implemented a consistent color scheme for service order types:

- **Service**: Blue
- **Installation**: Green
- **Inspection**: Yellow

This color scheme is used consistently across the application in the service order list, detail view, and calendar.

## Benefits

1. **Improved Visual Organization**: Users can quickly identify different types of service orders in the calendar view.
2. **Enhanced Filtering**: Users can filter service orders by type in both the list and calendar views.
3. **Better Data Organization**: The explicit type field provides clear categorization of service orders.
4. **Streamlined Scheduling**: Technicians can easily see what type of work is scheduled for each day.

## Future Enhancements

1. **Type-Specific Forms**: Create specialized forms for each service order type with fields relevant to that type.
2. **Resource Allocation**: Implement resource allocation based on service order type (e.g., different technicians for installations vs. service).
3. **Reporting**: Add reporting capabilities to analyze workload by service order type.
4. **Mobile Optimization**: Enhance the mobile view to clearly show service order types in the calendar.

## Conclusion

The enhanced calendar functionality with support for multiple service order types improves the usability and organization of the HVAC CRM Servicetool. Users can now easily distinguish between different types of service orders in the calendar view, making scheduling and planning more efficient.