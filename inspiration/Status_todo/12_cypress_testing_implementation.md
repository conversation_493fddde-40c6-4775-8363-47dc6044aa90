# Cypress Testing Implementation

## Overview

This document outlines the implementation of comprehensive end-to-end testing for the HVAC CRM "Servicetool" application using Cypress. The testing infrastructure covers all major features of the application, ensuring that the application functions correctly from a user's perspective.

## Implementation Details

### 1. Cypress Configuration

The Cypress testing environment has been configured with the following settings:

- **Viewport Size**: 1280x800 pixels
- **Timeouts**:
  - Default Command Timeout: 10 seconds
  - Request Timeout: 10 seconds
  - Response Timeout: 30 seconds
  - Page Load Timeout: 60 seconds
- **Retries**: 2 retries in run mode, 0 in open mode
- **Base URL**: http://localhost:3000 (development) or http://localhost:8811 (test)

### 2. Custom Commands

Custom Cypress commands have been implemented to simplify test creation and maintenance:

- `login`: Logs in with a random user or specified credentials
- `cleanupUser`: Deletes the current user
- `visitAndCheck`: Extends the standard visit command to wait for the page to load
- `createCustomer`: Creates a customer via UI
- `createDevice`: Creates a device via UI
- `createServiceOrder`: Creates a service order via UI
- `createCalendarEntry`: Creates a calendar entry via UI

These commands encapsulate common operations and reduce code duplication across tests.

### 3. Test Fixtures

Test fixtures have been created for various entities to provide consistent test data:

- `customers.json`: Sample customer data
- `devices.json`: Sample device data
- `service-orders.json`: Sample service order data

### 4. Test Suites

Comprehensive test suites have been implemented for all major features:

#### Customer Management Tests

- Display customer list
- Create new customer
- View customer details
- Edit customer
- Delete customer

#### Device Management Tests

- Display device list
- Create new device
- View device details
- Edit device
- Delete device
- Filter devices by customer

#### Service Order Management Tests

- Display service order list
- Create new service order
- View service order details
- Edit service order
- Delete service order
- Filter service orders by status and priority

#### Calendar Functionality Tests

- Display calendar
- Create new calendar entry
- View calendar entry details
- Edit calendar entry
- Delete calendar entry
- Switch between calendar views
- Navigate between dates

#### Semantic Search Tests

- Display search page
- Find results based on exact text match
- Find results based on semantic search
- Filter search results by entity type
- Navigate to entity details from search results
- Show no results message
- Reindex entities

#### OCR Processing Tests

- Display invoice upload page
- Create invoice manually
- Upload invoice for OCR processing
- Display service report upload page
- Create service report manually
- Upload service report for OCR processing

#### Predictive Maintenance Tests

- Display device telemetry page
- Record device telemetry
- Display maintenance predictions
- Display telemetry visualizations
- Mark maintenance as performed

#### Authentication Tests

- Register new user
- Login with valid credentials
- Show error message for invalid login
- Logout user
- Redirect to login page for protected routes
- Respect role-based access control

#### Dashboard Tests

- Display dashboard page
- Display key metrics
- Display recent service orders
- Display upcoming calendar events
- Display quick actions
- Navigate to corresponding pages from quick actions
- Display charts and visualizations
- Display role-specific dashboard content

#### Bielik LLM Integration Tests

- Display AI assistant interface
- Ask questions to the AI assistant
- Provide device-specific recommendations
- Provide service order analysis
- Generate service reports with AI
- Provide customer insights with AI
- Allow conversation history with the AI assistant

### 5. NPM Scripts

NPM scripts have been added to package.json to run specific test suites:

```json
"test:e2e:dev": "start-server-and-test dev http://localhost:3000 \"npx cypress open\"",
"test:e2e:run": "cross-env PORT=8811 start-server-and-test start:mocks http://localhost:8811 \"npx cypress run\"",
"test:e2e:customers": "cross-env PORT=8811 start-server-and-test start:mocks http://localhost:8811 \"npx cypress run --spec 'cypress/e2e/customers/**/*.cy.ts'\"",
"test:e2e:devices": "cross-env PORT=8811 start-server-and-test start:mocks http://localhost:8811 \"npx cypress run --spec 'cypress/e2e/devices/**/*.cy.ts'\"",
"test:e2e:service-orders": "cross-env PORT=8811 start-server-and-test start:mocks http://localhost:8811 \"npx cypress run --spec 'cypress/e2e/service-orders/**/*.cy.ts'\"",
"test:e2e:calendar": "cross-env PORT=8811 start-server-and-test start:mocks http://localhost:8811 \"npx cypress run --spec 'cypress/e2e/calendar/**/*.cy.ts'\"",
"test:e2e:search": "cross-env PORT=8811 start-server-and-test start:mocks http://localhost:8811 \"npx cypress run --spec 'cypress/e2e/search/**/*.cy.ts'\"",
"test:e2e:ocr": "cross-env PORT=8811 start-server-and-test start:mocks http://localhost:8811 \"npx cypress run --spec 'cypress/e2e/ocr/**/*.cy.ts'\"",
"test:e2e:predictive": "cross-env PORT=8811 start-server-and-test start:mocks http://localhost:8811 \"npx cypress run --spec 'cypress/e2e/predictive-maintenance/**/*.cy.ts'\"",
"test:e2e:auth": "cross-env PORT=8811 start-server-and-test start:mocks http://localhost:8811 \"npx cypress run --spec 'cypress/e2e/auth/**/*.cy.ts'\"",
"test:e2e:dashboard": "cross-env PORT=8811 start-server-and-test start:mocks http://localhost:8811 \"npx cypress run --spec 'cypress/e2e/dashboard/**/*.cy.ts'\"",
"test:e2e:ai": "cross-env PORT=8811 start-server-and-test start:mocks http://localhost:8811 \"npx cypress run --spec 'cypress/e2e/ai/**/*.cy.ts'\""
```

## Usage

### Running Tests

To run all tests in interactive mode:

```sh
npm run test:e2e:dev
```

To run all tests in headless mode:

```sh
npm run test:e2e:run
```

To run specific test suites:

```sh
npm run test:e2e:customers    # Customer management tests
npm run test:e2e:devices      # Device management tests
npm run test:e2e:service-orders # Service order tests
npm run test:e2e:calendar     # Calendar tests
npm run test:e2e:search       # Semantic search tests
npm run test:e2e:ocr          # OCR processing tests
npm run test:e2e:predictive   # Predictive maintenance tests
npm run test:e2e:auth         # Authentication tests
npm run test:e2e:dashboard    # Dashboard tests
npm run test:e2e:ai           # AI integration tests
```

### Writing New Tests

When writing new tests:

1. Place them in the appropriate directory under `cypress/e2e/`
2. Use the custom commands where appropriate to reduce code duplication
3. Follow the existing patterns for consistent test structure
4. Use the `cy.findByRole` and `cy.findByLabelText` selectors from Testing Library for better accessibility testing

## Next Steps

1. **Implement CI/CD Pipeline**: Set up GitHub Actions or another CI/CD service to run tests automatically on pull requests and deployments.
2. **Add Visual Regression Testing**: Consider adding visual regression testing with Cypress and Percy or a similar tool.
3. **Improve Test Coverage**: Continue to add tests for edge cases and error scenarios.
4. **Performance Testing**: Add performance testing to ensure the application meets performance requirements.

## Conclusion

The Cypress testing implementation provides comprehensive end-to-end testing for the HVAC CRM "Servicetool" application. This ensures that the application functions correctly from a user's perspective and helps prevent regressions when making changes to the codebase.