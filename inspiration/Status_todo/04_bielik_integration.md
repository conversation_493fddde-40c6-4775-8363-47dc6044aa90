# Integracja z Bielik LLM

## Wprowadzenie

Integracja z Bielik LLM jest kluczowym elementem projektu HVAC CRM "Servicetool". Bielik LLM to zaawansowany model językowy, który będzie wykorzystywany do analizy danych, generowania raportów, asystowania technikom i automatyzacji procesów. Dokument ten opisuje plan integracji Bielik LLM z nowym, zunifikowanym stosem technologicznym opartym na Remix.

## Architektura Integracji

```
┌─────────────────────────────────────────┐
│                                         │
│             Remix Frontend              │
│                                         │
└───────────────────┬─────────────────────┘
                    │
┌───────────────────▼─────────────────────┐
│                                         │
│           Remix API Endpoints           │
│                                         │
└───┬───────────────────────────────┬─────┘
    │                               │
┌───▼───────────────┐     ┌─────────▼─────────┐
│                   │     │                   │
│  SQLite/Prisma    │     │    Bielik LLM     │
│                   │     │    Connector      │
└─────────┬─────────┘     └─────────┬─────────┘
          │                         │
          │           ┌─────────────▼─────────┐
          │           │                       │
          │           │   Bielik LLM API      │
          │           │   (Port 8877)         │
          │           │                       │
          │           └───────────────────────┘
          │
┌─────────▼─────────┐
│                   │
│      Qdrant       │
│                   │
└───────────────────┘
```

## Komponenty Integracji

### 1. Bielik LLM Connector

Moduł odpowiedzialny za komunikację z API Bielik LLM. Będzie implementował następujące funkcjonalności:

- Zarządzanie połączeniem z API Bielik LLM
- Obsługa zapytań i odpowiedzi
- Zarządzanie kontekstem konwersacji
- Obsługa błędów i retry logic

```typescript
// app/services/bielik.server.ts
import { Configuration, OpenAIApi } from 'openai';

const BIELIK_API_URL = process.env.BIELIK_API_URL || 'http://host.docker.internal:8877';

const configuration = new Configuration({
  basePath: BIELIK_API_URL,
});

const bielikApi = new OpenAIApi(configuration);

export async function generateCompletion(prompt: string, options = {}) {
  try {
    const response = await bielikApi.createCompletion({
      model: 'bielik-4.5b-v3.0-instruct',
      prompt,
      max_tokens: 1000,
      ...options,
    });
    
    return response.data.choices[0].text;
  } catch (error) {
    console.error('Error calling Bielik LLM:', error);
    throw error;
  }
}

export async function generateEmbedding(text: string) {
  try {
    const response = await bielikApi.createEmbedding({
      model: 'bielik-4.5b-v3.0-instruct',
      input: text,
    });
    
    return response.data.data[0].embedding;
  } catch (error) {
    console.error('Error generating embedding with Bielik LLM:', error);
    throw error;
  }
}
```

### 2. Integracja z Qdrant

Moduł odpowiedzialny za przechowywanie i wyszukiwanie wektorów wygenerowanych przez Bielik LLM.

```typescript
// app/services/qdrant.server.ts (rozszerzenie istniejącego pliku)

import { generateEmbedding } from './bielik.server';

// Zastąpienie mock funkcji rzeczywistą implementacją
async function generateEmbedding(text: string): Promise<number[]> {
  return await bielikService.generateEmbedding(text);
}
```

### 3. GraphQL API dla Bielik LLM

Rozszerzenie schematu GraphQL o typy i resolwery związane z Bielik LLM.

```typescript
// app/graphql/schema.ts (rozszerzenie istniejącego pliku)

const typeDefs = `#graphql
  # Istniejące typy...

  type BielikCompletion {
    text: String!
    created: String!
  }

  type BielikEmbedding {
    embedding: [Float!]!
    created: String!
  }

  extend type Query {
    # Istniejące zapytania...
    
    generateCompletion(prompt: String!, maxTokens: Int): BielikCompletion!
    semanticSearch(query: String!, collection: String!, limit: Int): [String!]!
  }
`;

const resolvers = {
  Query: {
    // Istniejące resolwery...
    
    generateCompletion: async (_: any, { prompt, maxTokens }: { prompt: string, maxTokens?: number }) => {
      const text = await bielikService.generateCompletion(prompt, { max_tokens: maxTokens });
      return {
        text,
        created: new Date().toISOString(),
      };
    },
    
    semanticSearch: async (_: any, { query, collection, limit }: { query: string, collection: string, limit?: number }) => {
      const results = await qdrantService.semanticSearch(query, collection, limit);
      return results.map(result => result.payload.text);
    },
  },
  
  // Istniejące resolwery...
};
```

## Przypadki Użycia

### 1. Asystent Technika

Bielik LLM będzie wykorzystywany do asystowania technikom w diagnozie i naprawie urządzeń HVAC.

```typescript
// Przykładowa implementacja asystenta technika
async function getTechnicianAssistance(problem: string, deviceModel: string) {
  const prompt = `
    Jako asystent technika HVAC, pomóż zdiagnozować i naprawić następujący problem:
    
    Model urządzenia: ${deviceModel}
    Opis problemu: ${problem}
    
    Podaj możliwe przyczyny problemu, kroki diagnostyczne i sugerowane rozwiązania.
  `;
  
  return await bielikService.generateCompletion(prompt);
}
```

### 2. Analiza Sentymentu Opinii Klientów

Bielik LLM będzie wykorzystywany do analizy sentymentu opinii klientów.

```typescript
// Przykładowa implementacja analizy sentymentu
async function analyzeSentiment(feedback: string) {
  const prompt = `
    Przeanalizuj poniższą opinię klienta i określ jej sentyment (pozytywny, neutralny, negatywny).
    Podaj również kluczowe punkty z opinii.
    
    Opinia: ${feedback}
  `;
  
  return await bielikService.generateCompletion(prompt);
}
```

### 3. Wyszukiwanie Semantyczne

Bielik LLM będzie wykorzystywany do generowania embedingów dla wyszukiwania semantycznego.

```typescript
// Przykładowa implementacja wyszukiwania semantycznego
async function searchServiceOrders(query: string) {
  // Generowanie embeddingu dla zapytania
  const embedding = await bielikService.generateEmbedding(query);
  
  // Wyszukiwanie podobnych zleceń serwisowych
  const results = await qdrantService.searchSimilar(COLLECTIONS.SERVICE_ORDERS, embedding);
  
  return results.map(result => result.payload);
}
```

## Plan Wdrożenia

| Etap | Opis | Czas |
|------|------|------|
| 1 | Implementacja Bielik LLM Connector | 1 tydzień |
| 2 | Integracja z Qdrant | 1 tydzień |
| 3 | Rozszerzenie GraphQL API | 1 tydzień |
| 4 | Implementacja przypadków użycia | 2 tygodnie |
| 5 | Testy i optymalizacja | 1 tydzień |

## Metryki Sukcesu

- **Dokładność odpowiedzi** - min. 85% poprawnych odpowiedzi w testach
- **Czas odpowiedzi** - max. 2 sekundy dla generowania tekstu
- **Trafność wyszukiwania** - min. 80% trafnych wyników w top 5
- **Użyteczność dla techników** - min. 4/5 w ankietach satysfakcji

## Ryzyka i Mitygacja

| Ryzyko | Mitygacja |
|--------|-----------|
| Niedostępność Bielik LLM | Implementacja fallbacku do innych modeli (OpenAI, Azure) |
| Problemy z wydajnością | Caching odpowiedzi, optymalizacja zapytań |
| Niedokładne odpowiedzi | Fine-tuning modelu, weryfikacja przez człowieka |
| Problemy z integracją | Szczegółowe testy, monitoring |