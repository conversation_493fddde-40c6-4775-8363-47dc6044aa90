# Calendar Semantic Analysis

This document describes the semantic analysis feature for calendar events in the HVAC CRM system.

## Overview

The calendar semantic analysis feature uses the Bielik LLM to extract valuable information from descriptive calendar events. This is particularly useful for Outlook calendar events, which often contain a wealth of information in free-text format.

## Features

- Automatic extraction of client information, contact details, and addresses
- Identification of device types, models, and quantities
- Detection of service types and technical issues
- Extraction of cost information and spare parts
- Categorization of events by type, priority, and status
- Keyword extraction for better searchability

## Architecture

The feature consists of the following components:

1. **Calendar Semantic Analyzer** (Python service)
   - Analyzes calendar events using Bielik LLM
   - Extracts structured information from free-text descriptions
   - Provides API endpoints for analysis

2. **Calendar Database Updater** (Python service)
   - Updates the database with semantic analysis results
   - Maps extracted entities to database fields

3. **Calendar Analysis Task** (Scheduled task)
   - Periodically processes new calendar events
   - Generates analysis reports

4. **Calendar Semantic Integration** (Integration service)
   - Connects the semantic analysis with other system components
   - Updates the memory bank with calendar insights

5. **Remix App Integration** (TypeScript service)
   - Provides API for the frontend to request semantic analysis
   - Updates the UI with semantic analysis results

## Implementation Details

### Database Schema

The `CalendarEntry` model has been extended with the following fields:

- `type`: Service, installation, inspection, meeting, etc.
- `customer`: Customer name extracted from event
- `technician`: Technician name extracted from event
- `device`: Device name extracted from event
- `deviceCount`: Number of devices
- `priority`: Low, medium, high, critical
- `status`: Scheduled, in progress, completed, cancelled
- `clientContact`: JSON string with client contact details
- `technicalIssues`: JSON string with technical issues
- `spareParts`: JSON string with spare parts
- `costAmount`: Cost amount
- `costCurrency`: Cost currency
- `costDescription`: Cost description
- `keywords`: JSON string with keywords
- `semanticAnalysis`: JSON string with full semantic analysis
- `analysisTimestamp`: When the analysis was performed

### API Endpoints

The Calendar Analysis API provides the following endpoints:

- `POST /api/calendar/analyze`: Analyze a single calendar entry
- `POST /api/calendar/batch-analyze`: Analyze multiple calendar entries in batch
- `GET /api/health`: Health check endpoint

### Integration with Outlook Calendar

The Outlook Calendar integration has been enhanced to automatically perform semantic analysis on newly synchronized events. This ensures that all calendar events are enriched with structured data extracted from their descriptions.

## Usage

### Automatic Analysis

Calendar events are automatically analyzed in the following scenarios:

1. When a new event is synchronized from Outlook Calendar
2. When a new event is created in the HVAC CRM system
3. When an existing event is updated with new information

### Manual Analysis

Users can also manually trigger semantic analysis for specific events:

1. Navigate to the calendar event details page
2. Click the "Analyze" button
3. View the extracted information in the event details

## Benefits

- **Improved Data Quality**: Automatically extract structured data from free-text descriptions
- **Enhanced Search**: Find events by client, device, or issue type
- **Better Insights**: Generate reports based on extracted data
- **Time Savings**: Reduce manual data entry by automatically extracting information
- **Consistency**: Ensure consistent categorization of events

## Future Enhancements

- Integration with customer database to match extracted client names with existing customers
- Integration with device database to match extracted device information with existing devices
- Enhanced reporting based on semantic analysis data
- Automatic service order creation based on calendar event analysis
- Predictive maintenance recommendations based on historical event analysis