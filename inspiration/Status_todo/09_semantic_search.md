# Implementacja Wyszukiwania Semantycznego

## Wprowadzenie

Wyszukiwanie semantyczne to zaawansowana technika wyszukiwania, która pozwala na znalezienie wyników na podstawie znaczenia, a nie tylko dokładnego dopasowania tekstu. W przeciwieństwie do tradycyjnego wyszukiwania, które opiera się na dopasowaniu słów kluczowych, wyszukiwanie semantyczne rozumie kontekst i znaczenie zapytania.

W projekcie HVAC CRM "Servicetool" zaimplementowaliśmy wyszukiwanie semantyczne wykorzystując model językowy Bielik LLM do generowania wektorów embedingowych oraz bazę wektorową Qdrant do przechowywania i wyszukiwania tych wektorów.

## Architektura

Architektura wyszukiwania semantycznego składa się z następujących komponentów:

1. **Bielik LLM** - model j<PERSON><PERSON><PERSON><PERSON> dostępny na porcie 8877, który generuje wektory embedingowe dla tekstu.
2. **Qdrant** - baza wektorowa, która przechowuje wektory embedingowe i umożliwia wyszukiwanie podobnych wektorów.
3. **Serwisy indeksujące** - komponenty, które automatycznie indeksują encje w Qdrant podczas ich tworzenia, aktualizacji i usuwania.
4. **API wyszukiwania** - endpointy API, które umożliwiają wyszukiwanie semantyczne.
5. **Interfejs użytkownika** - komponenty UI, które umożliwiają użytkownikowi korzystanie z wyszukiwania semantycznego.

## Implementacja

### 1. Integracja z Bielik LLM

Zaimplementowaliśmy serwis `bielik.server.ts`, który zapewnia integrację z modelem Bielik LLM. Serwis ten udostępnia funkcje do generowania tekstu i wektorów embedingowych.

```typescript
// Przykład generowania wektora embedingowego
export async function generateEmbedding(text: string): Promise<number[]> {
  try {
    const bielikApi = getBielikApi();
    
    const response = await bielikApi.createEmbedding({
      model: BIELIK_MODEL,
      input: text,
    });
    
    return response.data.data[0].embedding;
  } catch (error) {
    console.error('Error generating embedding with Bielik LLM:', error);
    throw new Error(`Failed to generate embedding: ${(error as Error).message}`);
  }
}
```

### 2. Integracja z Qdrant

Zaimplementowaliśmy serwis `qdrant.server.ts`, który zapewnia integrację z bazą wektorową Qdrant. Serwis ten udostępnia funkcje do inicjalizacji kolekcji, indeksowania encji i wyszukiwania podobnych wektorów.

```typescript
// Przykład wyszukiwania podobnych wektorów
export async function searchSimilar(
  collectionName: string,
  query: string,
  limit: number = 5
) {
  try {
    const queryEmbedding = await generateEmbedding(query);
    
    const searchResults = await qdrantClient.search(collectionName, {
      vector: queryEmbedding,
      limit,
      with_payload: true,
    });
    
    return searchResults;
  } catch (error) {
    console.error(`Error searching in ${collectionName}:`, error);
    return [];
  }
}
```

### 3. Automatyczne indeksowanie encji

Zaimplementowaliśmy automatyczne indeksowanie encji w Qdrant podczas ich tworzenia, aktualizacji i usuwania. Każdy serwis encji (customer, device, service-order, note) zawiera kod do indeksowania encji w Qdrant.

```typescript
// Przykład indeksowania klienta w Qdrant
export async function createCustomer(
  data: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>,
  userId: string
): Promise<ServiceResponse<Customer>> {
  try {
    // Create customer in database
    const customer = await prisma.customer.create({
      data: {
        ...data,
        userId,
      },
    });

    // Index customer in Qdrant for semantic search
    try {
      await qdrantService.indexCustomer(prisma, customer.id);
    } catch (indexError) {
      console.error('Failed to index customer in Qdrant:', indexError);
      // Don't fail the operation if indexing fails
    }

    return {
      data: customer,
      error: null,
      success: true
    };
  } catch (error) {
    console.error('Failed to create customer:', error);
    return {
      data: null,
      error: 'Failed to create customer',
      success: false
    };
  }
}
```

### 4. API wyszukiwania semantycznego

Zaimplementowaliśmy API wyszukiwania semantycznego, które umożliwia wyszukiwanie encji na podstawie zapytania tekstowego. API to jest dostępne pod adresem `/api/semantic-search`.

```typescript
// Przykład API wyszukiwania semantycznego
export async function action({ request }: ActionFunctionArgs) {
  const userId = await requireUserId(request);
  
  try {
    // Parse the request body
    const body = await request.json();
    const { query, collection, limit = 10, useSemanticSearch = true } = body;
    
    if (!query) {
      return json({ success: false, error: "Query is required", data: [] }, { status: 400 });
    }
    
    // Perform the search
    const searchResponse = await unifiedSearch(query, userId, {
      useSemanticSearch,
      collection,
      limit,
    });
    
    return json(searchResponse);
  } catch (error) {
    console.error("Error in semantic search API:", error);
    return json(
      { 
        success: false, 
        error: `Failed to perform search: ${(error as Error).message}`,
        data: []
      },
      { status: 500 }
    );
  }
}
```

### 5. Interfejs użytkownika wyszukiwania

Zaimplementowaliśmy interfejs użytkownika wyszukiwania, który umożliwia użytkownikowi korzystanie z wyszukiwania semantycznego. Interfejs ten składa się z formularza wyszukiwania i komponentu wyników wyszukiwania.

```tsx
// Przykład formularza wyszukiwania semantycznego
export function SemanticSearchForm({
  defaultQuery = "",
  defaultCollection = "all",
  defaultLimit = 10,
  onSearch,
}: SemanticSearchFormProps) {
  const [query, setQuery] = useState(defaultQuery);
  const [collection, setCollection] = useState(defaultCollection);
  const [limit, setLimit] = useState(defaultLimit);
  const [useSemanticSearch, setUseSemanticSearch] = useState(true);
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (onSearch) {
      onSearch(query, collection, limit, useSemanticSearch);
    }
  };
  
  // ...
}
```

## Zarządzanie indeksem

Zaimplementowaliśmy również stronę do zarządzania indeksem wyszukiwania semantycznego, która umożliwia przeindeksowanie encji. Strona ta jest dostępna pod adresem `/search/index-management`.

```tsx
// Przykład strony zarządzania indeksem
export default function IndexManagementPage() {
  const actionData = useActionData<typeof action>();
  const [entityType, setEntityType] = useState("customer");
  const [userScope, setUserScope] = useState("own");
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const handleSubmit = (e: React.FormEvent) => {
    setIsSubmitting(true);
    // Form will be submitted normally, and the action function will handle it
  };
  
  // ...
}
```

## Korzyści

Wyszukiwanie semantyczne zapewnia następujące korzyści:

1. **Lepsze wyniki wyszukiwania** - wyszukiwanie semantyczne rozumie kontekst i znaczenie zapytania, co pozwala na znalezienie bardziej trafnych wyników.
2. **Obsługa języka naturalnego** - użytkownicy mogą wyszukiwać używając języka naturalnego, a nie tylko słów kluczowych.
3. **Wyszukiwanie kontekstowe** - wyszukiwanie semantyczne może znaleźć wyniki, które są kontekstowo powiązane z zapytaniem, nawet jeśli nie zawierają dokładnych słów kluczowych.
4. **Wielojęzyczność** - model Bielik LLM obsługuje język polski, co jest kluczowe dla projektu.

## Przyszłe rozszerzenia

W przyszłości planujemy rozszerzyć wyszukiwanie semantyczne o następujące funkcje:

1. **Filtrowanie wyników** - dodanie możliwości filtrowania wyników wyszukiwania semantycznego.
2. **Sortowanie wyników** - dodanie możliwości sortowania wyników wyszukiwania semantycznego.
3. **Wyszukiwanie hybrydowe** - połączenie wyszukiwania semantycznego z tradycyjnym wyszukiwaniem w celu uzyskania jeszcze lepszych wyników.
4. **Personalizacja wyników** - dostosowanie wyników wyszukiwania do preferencji użytkownika.
5. **Analiza zapytań** - analiza zapytań użytkowników w celu poprawy jakości wyszukiwania.

## Podsumowanie

Wyszukiwanie semantyczne to potężna funkcja, która znacznie poprawia doświadczenie użytkownika w systemie HVAC CRM "Servicetool". Dzięki integracji z modelem Bielik LLM i bazą wektorową Qdrant, użytkownicy mogą wyszukiwać encje na podstawie znaczenia, a nie tylko dokładnego dopasowania tekstu.

Implementacja wyszukiwania semantycznego jest zgodna z filozofią projektu, która zakłada wykorzystanie zaawansowanych technologii AI do poprawy efektywności pracy techników HVAC.