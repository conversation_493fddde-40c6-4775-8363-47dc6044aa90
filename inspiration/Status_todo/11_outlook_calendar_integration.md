# Microsoft Outlook Calendar Integration

## Overview

This document outlines the implementation of Microsoft Outlook Calendar integration in the HVAC CRM Servicetool. The integration allows for bidirectional synchronization of calendar events between Microsoft Outlook and the HVAC CRM system, enabling technicians and administrators to manage their schedules efficiently.

## Implementation Details

### 1. OAuth Authentication

The integration uses OAuth 2.0 to authenticate with Microsoft Graph API:

- **Authorization Flow**: OAuth 2.0 Authorization Code Flow
- **Scopes Required**: `Calendars.ReadWrite`, `offline_access`
- **Token Management**: Secure storage of access and refresh tokens in the database
- **Token Refresh**: Automatic refresh of access tokens when they expire

### 2. Microsoft Graph API Client

A dedicated client for Microsoft Graph API handles all API interactions:

- **Connection Management**: Handles authentication and token refresh
- **Error Handling**: Comprehensive error handling with retry logic
- **Rate Limiting**: Respects Microsoft Graph API rate limits
- **Logging**: Detailed logging for troubleshooting

### 3. Calendar Synchronization

The synchronization process includes:

- **Bidirectional Sync**: Events can be created, updated, or deleted in either system
- **Conflict Resolution**: Smart conflict resolution based on last modified timestamp
- **Scheduled Sync**: Automatic synchronization every 30 minutes during business hours (7:00-17:00)
- **Manual Sync**: Option for users to trigger synchronization manually
- **Selective Sync**: Configuration options for which calendars and event types to sync

### 4. Data Mapping

Mapping between Outlook Calendar events and HVAC CRM calendar entries:

| Outlook Calendar Field | HVAC CRM Field |
|------------------------|----------------|
| `subject`              | `title`        |
| `bodyPreview`          | `description`  |
| `start.dateTime`       | `startTime`    |
| `end.dateTime`         | `endTime`      |
| `location.displayName` | `location`     |
| `isAllDay`             | `isAllDay`     |
| `id`                   | `outlookEventId` |

### 5. User Interface

The user interface includes:

- **Connection Setup**: UI for connecting to Microsoft Outlook
- **Sync Settings**: Configuration options for synchronization
- **Sync Status**: Indicators for sync status and last sync time
- **Manual Sync**: Button for triggering manual synchronization
- **Visual Indicators**: Clear indication of which events are synced with Outlook

## Technical Architecture

### Components

1. **OAuth Service**: Handles authentication with Microsoft Graph API
2. **Graph API Client**: Manages API requests to Microsoft Graph
3. **Sync Service**: Coordinates synchronization between systems
4. **Scheduler**: Manages scheduled synchronization jobs
5. **UI Components**: User interface for managing integration

### Database Schema

The database schema includes:

- `OutlookCalendarIntegration`: Stores OAuth tokens and sync settings
- `CalendarEntry`: Includes fields for tracking Outlook event IDs and sync status

## Implementation Plan

1. **Phase 1: OAuth Authentication**
   - Implement OAuth routes
   - Create token storage and refresh mechanism
   - Add UI for connecting to Outlook

2. **Phase 2: Basic Synchronization**
   - Implement Microsoft Graph API client
   - Create basic one-way sync from Outlook to HVAC CRM
   - Add manual sync trigger

3. **Phase 3: Advanced Synchronization**
   - Implement bidirectional sync
   - Add conflict resolution
   - Create scheduled sync job

4. **Phase 4: UI Enhancements**
   - Add sync status indicators
   - Implement sync settings UI
   - Create visual indicators for synced events

5. **Phase 5: Testing and Optimization**
   - Comprehensive testing
   - Performance optimization
   - Documentation

## Benefits

1. **Improved Scheduling**: Technicians can manage their schedules in either system
2. **Reduced Double-Entry**: No need to manually enter events in both systems
3. **Better Visibility**: Clear view of all scheduled events across systems
4. **Increased Efficiency**: Automated synchronization saves time and reduces errors
5. **Seamless Integration**: Works with existing Microsoft tools used by the organization

## Conclusion

The Microsoft Outlook Calendar integration enhances the HVAC CRM Servicetool by providing seamless calendar synchronization with Microsoft Outlook. This integration improves scheduling efficiency, reduces manual data entry, and provides better visibility into technician schedules.
