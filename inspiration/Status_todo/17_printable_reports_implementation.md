# Printable Reports and Invoices Implementation

## Overview

The printable reports and invoices feature enhances the HVAC CRM by providing professionally formatted, printable documents for service reports and invoices. This implementation includes:

1. **Company Settings Management**: A dedicated interface for configuring company information
2. **Customizable Templates**: Professional templates for invoices and service reports
3. **PDF Generation**: Ability to generate and download PDF documents
4. **Print Functionality**: Direct printing from the browser
5. **Preview Capability**: Preview documents before printing or downloading

This feature is particularly valuable for HVAC companies that need to provide professional documentation to customers, ensuring consistent branding and comprehensive information in all customer-facing documents.

## Database Schema Changes

A new `CompanySettings` model has been added to store company information:

```prisma
model CompanySettings {
  id                String   @id @default(cuid())
  name              String
  address           String?
  city              String?
  postalCode        String?
  country           String?
  phone             String?
  email             String?
  website           String?
  taxId             String?
  logoUrl           String?
  primaryColor      String?
  secondaryColor    String?
  bankName          String?
  bankAccountNumber String?
  bankSwift         String?
  invoiceFooter     String?
  reportFooter      String?
  termsAndConditions String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
}
```

## New Components

### Templates

1. **PrintableInvoice**: A React component for rendering printable invoices
   - Professional layout with company branding
   - Sections for invoice details, customer information, line items, and totals
   - Support for custom footer text and terms & conditions
   - Responsive design that works well for printing

2. **PrintableServiceReport**: A React component for rendering printable service reports
   - Professional layout with company branding
   - Sections for service details, customer information, device information, work performed, and recommendations
   - Support for photos and signatures
   - Custom footer text

## New Services

### Company Settings Service

A new service has been implemented to manage company settings:

- `getCompanySettings`: Retrieves company settings or creates default settings if none exist
- `updateCompanySettings`: Updates company settings
- `getCompanyInfoForPrintableDocuments`: Formats company information for use in printable documents

### PDF Generation Utility

A utility for generating PDF documents has been enhanced:

- `generatePDFFromElement`: Generates a PDF from an HTML element
- `generatePDFFromHTML`: Generates a PDF from HTML content
- `savePDF`: Saves a PDF to file
- `openPDF`: Opens a PDF in a new window
- `getPDFDataURL`: Gets a PDF as a data URL
- `getPDFBlob`: Gets a PDF as a blob

## New Routes

The following routes have been added to support printable reports and invoices:

- `/settings/company`: Allows users to configure company information
- `/settings/preview-invoice`: Provides a preview of how invoices will look
- `/settings/preview-report`: Provides a preview of how service reports will look

## Enhanced Routes

The following existing routes have been enhanced:

- `/invoices/$invoiceId`: Now includes PDF generation and printing capabilities
- `/service-reports/$reportId`: Now includes PDF generation and printing capabilities

## User Interface

The user interface has been enhanced with:

- Company settings form with sections for:
  - Company information
  - Branding
  - Banking information
  - Document settings
- Preview pages for invoices and service reports
- Print and download buttons on invoice and service report detail pages
- Modal dialogs for print preview

## Usage Examples

### Configuring Company Information

1. Navigate to Settings > Company
2. Fill in company information, branding, banking details, and document settings
3. Save settings

### Previewing Documents

1. Navigate to Settings > Company
2. Click "Preview Invoice" or "Preview Report" to see how documents will look
3. Use the print or download buttons to test printing and PDF generation

### Printing or Downloading an Invoice

1. Navigate to Invoices > [Invoice ID]
2. Click "Print" to open the print dialog
3. Click "Download PDF" to generate and download a PDF

### Printing or Downloading a Service Report

1. Navigate to Service Reports > [Report ID]
2. Click "Print" to open the print dialog
3. Click "Download PDF" to generate and download a PDF

## Future Enhancements

1. **Template Customization**: Allow users to customize the layout and design of templates
2. **Multiple Templates**: Support for different templates for different purposes
3. **Batch Printing**: Allow printing or downloading multiple documents at once
4. **Email Integration**: Send documents directly via email
5. **Digital Signatures**: Add support for digital signatures
6. **QR Codes**: Include QR codes for easy access to online versions of documents
7. **Localization**: Support for multiple languages and regional formatting

## Conclusion

The printable reports and invoices feature significantly enhances the HVAC CRM's usability by providing professional, customizable documents for customer communication. This feature is particularly valuable for HVAC companies that need to maintain a professional image and provide comprehensive documentation to customers.