# Status i Plan Projektu HVAC CRM "Servicetool"

Ten folder zawiera dokumentację dotyczącą unifikacji technologicznej projektu HVAC CRM "Servicetool" z wykorzystaniem Remix, SQLite i Qdrant.

## Zawartość Folderu

1. [**Przegląd**](./01_overview.md) - Ogólny przegląd projektu i wybranej architektury
2. [**Mapa Drogowa Integracji**](./02_integration_roadmap.md) - Szczegółowy plan integracji i rozwoju projektu
3. [**Ko<PERSON><PERSON>ści**](./03_benefits.md) - Korzyści techniczne i biznesowe wynikające z unifikacji technologicznej
4. [**Integracja z Bielik LLM**](./04_bielik_integration.md) - Plan integracji z modelem językowym Bielik
5. [**Architektura**](./05_architecture.md) - Szczegółowy opis architektury systemu
6. [**Podsumowanie**](./06_summary.md) - Podsumowanie projektu i plan dalszych działań

## Kluczowe Technologie

- **Remix** - Nowoczesny framework fullstackowy JavaScript/TypeScript
- **SQLite** - Lekka, lokalna baza danych relacyjna
- **Qdrant** - Baza wektorowa do wyszukiwania semantycznego
- **GraphQL** - Warstwa API zapewniająca elastyczny dostęp do danych
- **Bielik LLM** - Zaawansowany model językowy do funkcji AI

## Fazy Projektu

1. **Fundament** (Zakończona) - Inicjalizacja projektu, konfiguracja baz danych i API
2. **Funkcjonalności Podstawowe** (Planowana) - Implementacja podstawowych funkcji biznesowych
3. **Integracja AI** (Planowana) - Integracja z Bielik LLM i wyszukiwanie semantyczne
4. **Integracje Zewnętrzne** (Planowana) - Integracja z systemami zewnętrznymi
5. **Optymalizacja** (Planowana) - Audyt bezpieczeństwa, optymalizacja wydajności, dokumentacja

## Jak Uruchomić Projekt

```bash
# Klonowanie repozytorium
git clone <repository-url>
cd hvac-remix

# Inicjalizacja projektu
./init-project.sh

# Uruchomienie środowiska deweloperskiego
docker-compose -f docker-compose.dev.yml up -d
```

## Kontakt

W przypadku pytań lub sugestii dotyczących projektu, prosimy o kontakt z zespołem deweloperskim.