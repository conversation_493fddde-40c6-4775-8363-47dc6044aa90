# Architektura Systemu HVAC CRM "Servicetool"

## Przegląd Architektury

System HVAC CRM "Servicetool" jest zbudowany w oparciu o nowoczesną, zunifikowaną architekturę, która łączy frontend i backend w jednym frameworku (Remix), wykorzystując SQLite jako główną bazę danych i Qdrant jako bazę wektorową. Architektura ta zapewnia prostotę, wydaj<PERSON>ść i łatwość lokalnego hostowania.

```
┌─────────────────────────────────────────────────────────────────────┐
│                                                                     │
│                         Warstwa Prezentacji                         │
│                                                                     │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐ │
│  │   Klienci   │  │ Urządzenia  │  │  Zlecenia   │  │  Kalendarz  │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘ │
│                                                                     │
└─────────────────────────────────┬───────────────────────────────────┘
                                  │
┌─────────────────────────────────▼───────────────────────────────────┐
│                                                                     │
│                         Warstwa Aplikacyjna                         │
│                                                                     │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐ │
│  │    Remix    │  │   GraphQL   │  │  Bielik LLM │  │    Qdrant   │ │
│  │   Routes    │  │     API     │  │  Connector  │  │  Connector  │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘ │
│                                                                     │
└─────────────────────────────────┬───────────────────────────────────┘
                                  │
┌─────────────────────────────────▼───────────────────────────────────┐
│                                                                     │
│                          Warstwa Danych                             │
│                                                                     │
│  ┌─────────────────────────┐        ┌─────────────────────────────┐ │
│  │                         │        │                             │ │
│  │      SQLite/Prisma      │        │           Qdrant            │ │
│  │                         │        │                             │ │
│  └─────────────────────────┘        └─────────────────────────────┘ │
│                                                                     │
└─────────────────────────────────────────────────────────────────────┘
```

## Komponenty Architektury

### 1. Warstwa Prezentacji

Warstwa prezentacji jest zbudowana w oparciu o komponenty React renderowane przez Remix. Wykorzystuje Atomic Design Pattern do organizacji komponentów.

#### Kluczowe Komponenty:

- **Atoms**: Podstawowe komponenty UI (przyciski, pola formularzy, ikony)
- **Molecules**: Złożone komponenty UI (formularze, karty, tabele)
- **Organisms**: Sekcje stron (nagłówki, stopki, panele boczne)
- **Templates**: Szablony stron
- **Pages**: Kompletne strony

### 2. Warstwa Aplikacyjna

Warstwa aplikacyjna jest sercem systemu, odpowiedzialnym za logikę biznesową, routing i komunikację z warstwą danych.

#### Kluczowe Komponenty:

- **Remix Routes**: Obsługa routingu i renderowania
- **GraphQL API**: Elastyczny dostęp do danych
- **Bielik LLM Connector**: Integracja z modelem językowym
- **Qdrant Connector**: Integracja z bazą wektorową

### 3. Warstwa Danych

Warstwa danych jest odpowiedzialna za przechowywanie i dostęp do danych.

#### Kluczowe Komponenty:

- **SQLite/Prisma**: Relacyjna baza danych z ORM
- **Qdrant**: Baza wektorowa do wyszukiwania semantycznego

## Przepływ Danych

### 1. Standardowy Przepływ Danych

```
┌──────────┐     ┌──────────┐     ┌──────────┐     ┌──────────┐
│          │     │          │     │          │     │          │
│  Klient  │────▶│  Remix   │────▶│  Prisma  │────▶│  SQLite  │
│          │     │  Route   │     │   ORM    │     │          │
│          │◀────│          │◀────│          │◀────│          │
└──────────┘     └──────────┘     └──────────┘     └──────────┘
```

1. Klient wysyła żądanie do odpowiedniej trasy Remix
2. Remix przetwarza żądanie i wywołuje odpowiednie funkcje loaderów/actions
3. Funkcje te korzystają z Prisma ORM do dostępu do danych w SQLite
4. Dane są zwracane do Remix, który renderuje odpowiedź
5. Odpowiedź jest wysyłana do klienta

### 2. Przepływ Danych GraphQL

```
┌──────────┐     ┌──────────┐     ┌──────────┐     ┌──────────┐
│          │     │          │     │          │     │          │
│  Klient  │────▶│ GraphQL  │────▶│ Resolwery│────▶│  Prisma  │────▶│  SQLite  │
│          │     │ Endpoint │     │          │     │   ORM    │     │          │
│          │◀────│          │◀────│          │◀────│          │◀────│          │
└──────────┘     └──────────┘     └──────────┘     └──────────┘     └──────────┘
```

1. Klient wysyła zapytanie GraphQL do endpointu GraphQL
2. Endpoint GraphQL przetwarza zapytanie i wywołuje odpowiednie resolwery
3. Resolwery korzystają z Prisma ORM do dostępu do danych w SQLite
4. Dane są zwracane do resolwerów, które formatują odpowiedź
5. Odpowiedź jest wysyłana do klienta

### 3. Przepływ Danych AI

```
┌──────────┐     ┌──────────┐     ┌──────────┐     ┌──────────┐
│          │     │          │     │          │     │          │
│  Klient  │────▶│  Remix   │────▶│  Bielik  │────▶│  Bielik  │
│          │     │  Route   │     │ Connector│     │   LLM    │
│          │     │          │     │          │     │          │
└──────────┘     └──────────┘     └──────────┘     └──────────┘
      ▲                                │
      │                                │
      │           ┌──────────┐         │
      │           │          │         │
      └───────────│  Remix   │◀────────┘
                  │  Route   │
                  │          │
                  └──────────┘
```

1. Klient wysyła żądanie do odpowiedniej trasy Remix
2. Remix przetwarza żądanie i wywołuje Bielik Connector
3. Bielik Connector komunikuje się z Bielik LLM
4. Bielik LLM generuje odpowiedź
5. Odpowiedź jest przetwarzana przez Remix i wysyłana do klienta

### 4. Przepływ Danych Wyszukiwania Semantycznego

```
┌──────────┐     ┌──────────┐     ┌──────────┐     ┌──────────┐
│          │     │          │     │          │     │          │
│  Klient  │────▶│  Remix   │────▶│  Bielik  │────▶│  Bielik  │
│          │     │  Route   │     │ Connector│     │   LLM    │
│          │     │          │     │          │     │          │
└──────────┘     └──────────┘     └──────────┘     └──────────┘
      ▲                                │
      │                                │
      │           ┌──────────┐     ┌──────────┐
      │           │          │     │          │
      └───────────│  Remix   │◀────│  Qdrant  │
                  │  Route   │     │          │
                  │          │     │          │
                  └──────────┘     └──────────┘
```

1. Klient wysyła zapytanie wyszukiwania do odpowiedniej trasy Remix
2. Remix przetwarza zapytanie i wywołuje Bielik Connector
3. Bielik Connector generuje embedding dla zapytania
4. Embedding jest używany do wyszukiwania w Qdrant
5. Wyniki są przetwarzane przez Remix i wysyłane do klienta

## Bezpieczeństwo

### Uwierzytelnianie i Autoryzacja

System wykorzystuje uwierzytelnianie oparte na sesjach z hasłami przechowywanymi w postaci hashowanej. Autoryzacja jest realizowana na poziomie tras Remix i resolwerów GraphQL.

```
┌──────────┐     ┌──────────┐     ┌──────────┐
│          │     │          │     │          │
│  Klient  │────▶│  Remix   │────▶│ Session  │
│          │     │  Route   │     │ Middleware│
│          │     │          │     │          │
└──────────┘     └──────────┘     └──────────┘
                       │                │
                       │                │
                       ▼                ▼
                  ┌──────────┐     ┌──────────┐
                  │          │     │          │
                  │ Protected│     │  SQLite  │
                  │  Route   │     │          │
                  │          │     │          │
                  └──────────┘     └──────────┘
```

### Bezpieczeństwo Danych

- Wszystkie dane są przechowywane lokalnie, co minimalizuje ryzyko wycieku
- Hasła są hashowane przed zapisaniem do bazy danych
- Dostęp do API jest chroniony przez uwierzytelnianie
- Dane wrażliwe są szyfrowane

## Skalowalność

Architektura systemu jest zaprojektowana z myślą o łatwej skalowalności:

- **Skalowalność Pozioma**: Możliwość uruchomienia wielu instancji aplikacji za load balancerem
- **Skalowalność Pionowa**: Możliwość zwiększenia zasobów serwera
- **Skalowalność Bazy Danych**: Możliwość migracji z SQLite do PostgreSQL w przypadku większych obciążeń

## Monitorowanie i Diagnostyka

System zawiera wbudowane mechanizmy monitorowania i diagnostyki:

- **Logi Aplikacji**: Szczegółowe logi działania aplikacji
- **Metryki Wydajności**: Monitorowanie czasu odpowiedzi, użycia zasobów
- **Alerty**: Automatyczne powiadomienia o problemach
- **Diagnostyka Błędów**: Szczegółowe informacje o błędach

## Podsumowanie

Architektura systemu HVAC CRM "Servicetool" jest zaprojektowana z myślą o prostocie, wydajności i łatwości lokalnego hostowania. Wykorzystanie zunifikowanego stosu technologicznego opartego na Remix, SQLite i Qdrant pozwala na szybkie wdrożenie i łatwe utrzymanie systemu, jednocześnie zapewniając zaawansowane funkcje, takie jak wyszukiwanie semantyczne i integracja z AI.