# Implementacja wsparcia offline w HVAC CRM

## Wprowadzenie

Wsparcie offline jest kluczową funkcjonalnością dla techników terenowych, któ<PERSON>y często pracują w miejscach o ograniczonym dostępie do internetu. Implementacja wsparcia offline w HVAC CRM umożliwia technikom:

1. Dostęp do danych klientów, urządzeń i zleceń serwisowych bez połączenia z internetem
2. Tworzenie i edycję zleceń serwisowych offline
3. Tworzenie raportów serwisowych offline
4. Automatyczną synchronizację danych po przywróceniu połączenia
5. Rozwiązywanie konfliktów synchronizacji

## Architektura

Implementacja wsparcia offline opiera się na następujących komponentach:

1. **Service Worker** - odpowiedzialny za przechwytywanie żądań sieciowych i obsługę trybu offline
2. **IndexedDB** - lokalna baza danych przechowująca dane offline
3. **Sync Manager** - zarządza synchronizacją danych między klientem a serwerem
4. **Conflict Resolution** - rozwiązuje konflikty synchronizacji
5. **Offline Indicator** - informuje użytkownika o statusie połączenia i synchronizacji

### Diagram architektury

```
+-------------------+     +-------------------+     +-------------------+
|                   |     |                   |     |                   |
|  Service Worker   |<--->|   Sync Manager   |<--->|   IndexedDB       |
|                   |     |                   |     |                   |
+-------------------+     +-------------------+     +-------------------+
         ^                        ^                         ^
         |                        |                         |
         v                        v                         v
+-------------------+     +-------------------+     +-------------------+
|                   |     |                   |     |                   |
|  Offline API      |<--->| Conflict          |<--->|  UI Components   |
|  Endpoints        |     | Resolution        |     |                  |
+-------------------+     +-------------------+     +-------------------+
```

## Komponenty

### 1. Service Worker (public/service-worker.js)

Service Worker jest odpowiedzialny za:

- Przechwytywanie żądań sieciowych
- Buforowanie statycznych zasobów
- Buforowanie odpowiedzi API
- Obsługę żądań w trybie offline
- Synchronizację danych w tle

Główne strategie buforowania:

- **Cache First** - dla statycznych zasobów (CSS, JS, obrazy)
- **Network First** - dla dynamicznych danych (API)
- **Stale While Revalidate** - dla danych referencyjnych

### 2. IndexedDB (app/utils/offline-storage.ts)

IndexedDB przechowuje:

- Zlecenia serwisowe
- Raporty serwisowe
- Dane klientów
- Dane urządzeń
- Dane referencyjne
- Dane użytkownika
- Kolejkę synchronizacji
- Konflikty synchronizacji

Główne funkcje:

- `saveOfflineData` - zapisuje dane offline
- `getAllOfflineData` - pobiera wszystkie dane offline
- `deleteOfflineData` - usuwa dane offline
- `updateOfflineData` - aktualizuje dane offline
- `addToSyncQueue` - dodaje element do kolejki synchronizacji
- `addConflict` - dodaje konflikt synchronizacji

### 3. Sync Manager (app/utils/sync-manager.ts)

Sync Manager zarządza:

- Synchronizacją danych między klientem a serwerem
- Rejestracją synchronizacji w tle
- Statusem synchronizacji
- Obsługą błędów synchronizacji

Główne funkcje:

- `syncAllData` - synchronizuje wszystkie dane
- `syncServiceOrders` - synchronizuje zlecenia serwisowe
- `syncServiceReports` - synchronizuje raporty serwisowe
- `syncCustomers` - synchronizuje dane klientów
- `syncDevices` - synchronizuje dane urządzeń
- `registerBackgroundSync` - rejestruje synchronizację w tle

### 4. Offline Sync Client (app/services/offlineSync.client.ts)

Offline Sync Client zapewnia:

- Inicjalizację wsparcia offline
- Rejestrację Service Worker
- Wykrywanie stanu online/offline
- Zarządzanie stanem synchronizacji
- Powiadamianie komponentów UI o zmianach stanu

Główne funkcje:

- `initOfflineSync` - inicjalizuje wsparcie offline
- `subscribeToOfflineSync` - subskrybuje zmiany stanu
- `synchronizeAllData` - synchronizuje wszystkie dane
- `saveServiceOrderOffline` - zapisuje zlecenie serwisowe offline
- `saveServiceReportOffline` - zapisuje raport serwisowy offline

### 5. API Endpoints (app/routes/api.sync.ts)

API Endpoints obsługują:

- Pobieranie danych do użytku offline
- Synchronizację danych offline z serwerem
- Rozwiązywanie konfliktów synchronizacji

Główne endpointy:

- `GET /api/sync?type=service-orders` - pobiera zlecenia serwisowe
- `GET /api/sync?type=service-reports` - pobiera raporty serwisowe
- `GET /api/sync?type=customers` - pobiera dane klientów
- `GET /api/sync?type=devices` - pobiera dane urządzeń
- `GET /api/sync?type=reference-data` - pobiera dane referencyjne
- `POST /api/sync` - synchronizuje dane offline z serwerem

### 6. UI Components

#### Offline Indicator (app/components/atoms/offline-indicator.tsx)

Informuje użytkownika o:

- Statusie połączenia (online/offline)
- Statusie synchronizacji (oczekująca/w trakcie/zakończona)
- Konfliktach synchronizacji

#### Sync Status (app/components/organisms/sync-status.tsx)

Wyświetla:

- Szczegółowy status synchronizacji
- Postęp synchronizacji
- Błędy synchronizacji
- Konflikty synchronizacji

#### Offline Page (app/routes/offline.tsx)

Wyświetla:

- Informację o braku połączenia
- Dostępne funkcje offline
- Przycisk do ponownej próby połączenia

## Przepływ danych

### Zapisywanie danych offline

1. Użytkownik tworzy/edytuje dane w trybie offline
2. Dane są zapisywane w IndexedDB
3. Element jest dodawany do kolejki synchronizacji
4. Offline Indicator informuje o oczekujących zmianach

### Synchronizacja danych

1. Po przywróceniu połączenia, Sync Manager wykrywa zmiany
2. Dane są wysyłane do serwera przez API Endpoints
3. Serwer przetwarza dane i zwraca odpowiedź
4. W przypadku sukcesu, dane są usuwane z kolejki synchronizacji
5. W przypadku konfliktu, konflikt jest zapisywany do rozwiązania

### Rozwiązywanie konfliktów

1. Użytkownik jest informowany o konfliktach przez Offline Indicator
2. Użytkownik przechodzi do strony rozwiązywania konfliktów
3. Użytkownik wybiera wersję danych do zachowania
4. Wybrana wersja jest zapisywana na serwerze
5. Konflikt jest usuwany z listy konfliktów

## Konfiguracja PWA

Aplikacja jest skonfigurowana jako Progressive Web App (PWA) z:

- Manifestem aplikacji (public/manifest.json)
- Ikonami aplikacji
- Service Worker
- Stroną offline

## Testowanie

Testowanie wsparcia offline obejmuje:

1. Testowanie w trybie offline (wyłączenie połączenia sieciowego)
2. Testowanie synchronizacji (przywrócenie połączenia)
3. Testowanie rozwiązywania konfliktów
4. Testowanie Service Worker
5. Testowanie buforowania

## Ograniczenia

Obecna implementacja ma następujące ograniczenia:

1. Ograniczona funkcjonalność w trybie offline
2. Brak wsparcia dla złożonych operacji wymagających połączenia z serwerem
3. Ograniczona przestrzeń dyskowa dla danych offline
4. Możliwe konflikty synchronizacji wymagające ręcznego rozwiązania

## Przyszłe rozszerzenia

Planowane rozszerzenia wsparcia offline:

1. Rozszerzenie funkcjonalności offline
2. Automatyczne rozwiązywanie konfliktów
3. Kompresja danych offline
4. Synchronizacja selektywna
5. Priorytetyzacja synchronizacji

## Podsumowanie

Implementacja wsparcia offline w HVAC CRM zapewnia technikom terenowym możliwość pracy bez połączenia z internetem, co znacząco zwiększa ich produktywność i efektywność. Dzięki zastosowaniu nowoczesnych technologii, takich jak Service Worker i IndexedDB, aplikacja oferuje płynne przejście między trybem online i offline, automatyczną synchronizację danych oraz rozwiązywanie konfliktów.
