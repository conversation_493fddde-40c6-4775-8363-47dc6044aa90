# Multi-Channel Notifications Implementation

## Overview

The multi-channel notifications system enhances the HVAC CRM by enabling notifications to be delivered through multiple channels:

1. **In-App Notifications**: Displayed within the application interface
2. **Email Notifications**: Sent to the user's email address
3. **SMS Notifications**: Sent to the user's phone number
4. **Push Notifications**: Delivered to mobile devices (placeholder for future implementation)

This system allows users to configure their notification preferences for different types of notifications and channels, ensuring they receive important information through their preferred communication methods.

## Database Schema Changes

### Notification Model

The Notification model has been extended to support multiple channels:

```prisma
model Notification {
  id          String @id @default(cuid())
  type        String // SERVICE_ORDER_CREATED, SERVICE_ORDER_UPDATED, etc.
  title       String
  message     String
  priority    String @default("medium") // low, medium, high
  status      String @default("unread") // unread, read, archived
  link        String? // Optional link to resource
  
  // Notification channels
  channels    String[] // IN_APP, EMAIL, SMS, PUSH
  
  // Delivery status
  emailSent   Boolean? // Whether email was sent
  smsSent     Boolean? // Whether SMS was sent
  pushSent    Boolean? // Whether push notification was sent
  
  // SMS data
  phoneNumber String? // Phone number for SMS
  
  // Email data
  emailAddress String? // Email address
  emailSubject String? // Email subject
  emailHtml    String? // Email HTML content
  
  // User relationship
  user        User @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  userId      String

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}
```

### UserSettings Model

The UserSettings model has been extended to support more detailed notification preferences:

```prisma
model UserSettings {
  // ... existing fields ...

  // Notification channels
  emailNotifications Boolean @default(true)
  inAppNotifications Boolean @default(true)
  pushNotifications Boolean @default(false)
  smsNotifications Boolean @default(false)
  
  // Notification types
  serviceOrderUpdates Boolean @default(true)
  calendarReminders Boolean @default(true)
  systemUpdates Boolean @default(true)
  deviceAlerts Boolean @default(true)
  newMessages Boolean @default(true)
  
  // Channel preferences for notification types (JSON)
  serviceOrderChannels String? // JSON array: ["IN_APP", "EMAIL", "SMS", "PUSH"]
  calendarReminderChannels String? // JSON array: ["IN_APP", "EMAIL", "SMS", "PUSH"]
  systemUpdateChannels String? // JSON array: ["IN_APP", "EMAIL", "SMS", "PUSH"]
  deviceAlertChannels String? // JSON array: ["IN_APP", "EMAIL", "SMS", "PUSH"]
  newMessageChannels String? // JSON array: ["IN_APP", "EMAIL", "SMS", "PUSH"]
  
  // Contact information for notifications
  notificationEmail String? // Alternative email for notifications
  notificationPhone String? // Phone number for SMS notifications

  // ... existing fields ...
}
```

## TypeScript Types

New TypeScript types have been added to support the multi-channel notifications:

```typescript
// Notification channel type
export type NotificationChannel = 'IN_APP' | 'EMAIL' | 'SMS' | 'PUSH';

// Basic notification settings
export type NotificationSettings = {
  email: boolean;
  inApp: boolean;
  push: boolean;
  sms: boolean;
  serviceOrderUpdates: boolean;
  calendarReminders: boolean;
  systemUpdates: boolean;
  deviceAlerts: boolean;
  newMessages: boolean;
};

// Advanced notification settings
export type AdvancedNotificationSettings = NotificationSettings & {
  serviceOrderChannels: NotificationChannel[];
  calendarReminderChannels: NotificationChannel[];
  systemUpdateChannels: NotificationChannel[];
  deviceAlertChannels: NotificationChannel[];
  newMessageChannels: NotificationChannel[];
  notificationEmail?: string;
  notificationPhone?: string;
};

// Extended notification object with delivery status
export type ExtendedNotification = Notification & {
  emailSent?: boolean;
  smsSent?: boolean;
  pushSent?: boolean;
  phoneNumber?: string;
  emailAddress?: string;
  emailSubject?: string;
  emailHtml?: string;
};
```

## New Services

### SMS Service

A new SMS service has been implemented to send SMS notifications:

- `sendSMS`: Sends a generic SMS message
- `sendServiceOrderNotificationSMS`: Sends a notification about a service order
- `sendCalendarReminderSMS`: Sends a reminder about a calendar event
- `sendDeviceAlertSMS`: Sends an alert about a device
- `shouldSendSMS`: Checks if a user should receive SMS notifications for a specific notification type

### Enhanced Email Service

The existing email service has been enhanced with new features:

- `shouldSendEmail`: Checks if a user should receive email notifications for a specific notification type
- New email templates for different notification types with improved styling
- Support for alternative email addresses for notifications

### Unified Notification Service

A new unified notification service has been implemented to handle sending notifications through multiple channels:

- `sendMultiChannelNotification`: Sends a notification through multiple channels based on user preferences

## User Interface

The notification settings UI has been enhanced with:

- Basic and advanced settings tabs
- Channel selection for each notification type
- Contact information settings for notifications
- Improved UI with better organization and visual feedback

## API Endpoints

A new API endpoint has been added for sending notifications:

- `POST /api/send-notification`: Sends a notification through multiple channels based on user preferences

## Usage Examples

### Sending a Service Order Notification

```typescript
import { sendMultiChannelNotification } from "~/services/notification.server";

// Send a notification about a new service order
await sendMultiChannelNotification({
  type: 'SERVICE_ORDER_CREATED',
  title: 'New Service Order',
  message: 'A new service order has been created',
  userId: user.id,
  priority: 'medium',
  serviceOrderId: serviceOrder.id,
  link: `/service-orders/${serviceOrder.id}`
});
```

### Sending a Calendar Reminder

```typescript
import { sendMultiChannelNotification } from "~/services/notification.server";

// Send a calendar reminder
await sendMultiChannelNotification({
  type: 'CALENDAR_REMINDER',
  title: 'Upcoming Event',
  message: 'You have an upcoming event',
  userId: user.id,
  priority: 'high',
  eventId: event.id,
  eventDate: event.startTime,
  location: event.location,
  link: `/calendar/events/${event.id}`
});
```

## Future Enhancements

1. **Push Notifications**: Implement push notifications for mobile devices using a service like Firebase Cloud Messaging or OneSignal
2. **Notification Templates**: Create more customizable notification templates for different notification types
3. **Notification Scheduling**: Allow scheduling notifications for future delivery
4. **Notification Analytics**: Track notification delivery and engagement metrics
5. **Notification Groups**: Allow grouping related notifications to reduce notification fatigue

## Conclusion

The multi-channel notifications system provides a flexible and powerful way to keep users informed about important events in the HVAC CRM system. By allowing users to configure their notification preferences, the system ensures that users receive information through their preferred communication channels, improving user experience and engagement.
