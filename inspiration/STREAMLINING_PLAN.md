# 🎯 HVAC-Remix Streamlining Plan

## Overview
Transform hvac-remix into a pure frontend application that communicates with GoBackend-Kratos for all backend operations.

## 🔍 Current Issues
- 11 moderate security vulnerabilities
- Redundant backend functionality
- Deprecated packages
- Excessive dependencies (169 total packages)

## 🗑️ Packages to Remove (Backend Functionality)

### Database & ORM
- `@prisma/client` - Database handled by GoBackend-Kratos
- `prisma` - Schema management in GoBackend
- `sqlite`, `sqlite3` - Database in GoBackend
- `@supabase/supabase-js` - Backend database operations

### Server & API
- `apollo-server-express` - GraphQL server (deprecated)
- `express` - Web server not needed
- `@trpc/server` - API server in GoBackend
- `@graphql-tools/schema` - Schema in GoBackend
- `graphql` - Server-side GraphQL

### AI & ML Services
- `openai` - AI calls from GoBackend
- `@azure/cognitiveservices-computervision` - OCR in GoBackend
- `@azure/identity`, `@azure/ms-rest-js` - Azure auth in GoBackend
- `@qdrant/js-client-rest` - Vector DB in GoBackend

### Authentication & Security
- `bcryptjs` - Password hashing in GoBackend
- `speakeasy` - 2FA in GoBackend
- `@sentry/node`, `@sentry/profiling-node` - Server monitoring

### Payment Processing
- `stripe` (server-side) - Keep only `@stripe/stripe-js`, `@stripe/react-stripe-js`

### Communication & Email
- `@microsoft/microsoft-graph-client` - Email/calendar in GoBackend

### Caching & Storage
- `redis` - Caching in GoBackend

## 🔄 Packages to Update (Security Fixes)

### High Priority
- `esbuild` - Update to latest version
- `estree-util-value-to-estree` - Fix prototype pollution
- `prismjs` - Fix DOM clobbering vulnerability
- `@copilotkit/react-ui` - Update to secure version

### Deprecated Packages
- `@remix-run/css-bundle` - Deprecated in Remix v2
- `apollo-server-express` - Replace with @apollo/server (if keeping GraphQL)

## ✅ Packages to Keep (Frontend Essential)

### Core Framework
- `@remix-run/node`, `@remix-run/react`, `@remix-run/serve`
- `react`, `react-dom`
- `typescript`

### UI Components
- `@radix-ui/*` - UI primitives
- `@headlessui/react`, `@heroicons/react`
- `@chakra-ui/*` - UI framework
- `lucide-react` - Icons

### Frontend Utilities
- `tailwindcss`, `tailwind-merge`, `tailwindcss-animate`
- `clsx`, `class-variance-authority`
- `framer-motion` - Animations
- `date-fns` - Date utilities
- `recharts` - Charts

### Client-side Services
- `@tanstack/react-query` - Data fetching
- `@trpc/client`, `@trpc/react-query` - API client
- `@stripe/stripe-js`, `@stripe/react-stripe-js` - Payment UI

### Development Tools
- Testing: `vitest`, `@testing-library/*`, `cypress`
- Linting: `eslint`, `prettier`
- Build: `vite`, `@vitejs/plugin-react`

## 🔧 Implementation Steps

1. **Security Fixes** - Update vulnerable packages
2. **Remove Backend Packages** - Uninstall server-side dependencies
3. **Update API Calls** - Point to GoBackend-Kratos endpoints
4. **Remove Backend Code** - Delete server-side logic
5. **Update Configuration** - Simplify build and deployment
6. **Test Integration** - Ensure GoBackend communication works

## 🎯 Expected Results

- **Reduced bundle size** by ~60%
- **Zero security vulnerabilities**
- **Faster build times**
- **Cleaner architecture**
- **Better separation of concerns**
- **Easier maintenance**

## 🔗 GoBackend-Kratos Integration

The frontend will communicate with GoBackend-Kratos via:
- HTTP REST APIs (`/api/v1/*`)
- gRPC-Web (if needed)
- WebSocket for real-time features
- AI services endpoint
- Email intelligence API
- Analytics dashboard API
