import React, { useState, useEffect, useRef } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Button, 
  Spin, 
  Alert, 
  Tabs, 
  Progress, 
  Tag, 
  List, 
  Statistic,
  Typography,
  Space,
  Divider,
  Badge
} from 'antd';
import { 
  RobotOutlined, 
  ThunderboltOutlined, 
  TrophyOutlined, 
  HeartOutlined,
  ToolOutlined,
  PlayCircleOutlined,
  StopOutlined,
  ReloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { request } from '@/request';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

const EnhancedAI = () => {
  const [loading, setLoading] = useState(false);
  const [streamingActive, setStreamingActive] = useState(false);
  const [streamingData, setStreamingData] = useState([]);
  const [performanceMetrics, setPerformanceMetrics] = useState(null);
  const [healthStatus, setHealthStatus] = useState(null);
  const [error, setError] = useState(null);
  const eventSourceRef = useRef(null);

  useEffect(() => {
    fetchPerformanceMetrics();
    fetchHealthStatus();
    
    // Cleanup on unmount
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
    };
  }, []);

  const fetchPerformanceMetrics = async () => {
    try {
      const response = await request.get({ entity: 'ai/enhanced/performance-metrics' });
      if (response.success) {
        setPerformanceMetrics(response.result);
      }
    } catch (err) {
      console.error('Performance metrics error:', err);
    }
  };

  const fetchHealthStatus = async () => {
    try {
      const response = await request.get({ entity: 'ai/enhanced/health' });
      if (response.success) {
        setHealthStatus(response.result);
      }
    } catch (err) {
      console.error('Health status error:', err);
    }
  };

  const startStreaming = () => {
    if (streamingActive) return;

    setStreamingActive(true);
    setStreamingData([]);
    setError(null);

    // Create EventSource for Server-Sent Events
    eventSourceRef.current = new EventSource('/api/ai/enhanced/stream-insights');

    eventSourceRef.current.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        setStreamingData(prev => [...prev, data]);
      } catch (err) {
        console.error('Streaming data parse error:', err);
      }
    };

    eventSourceRef.current.onerror = (error) => {
      console.error('Streaming error:', error);
      setError('Błąd podczas strumieniowania AI insights');
      stopStreaming();
    };

    eventSourceRef.current.addEventListener('complete', () => {
      stopStreaming();
    });
  };

  const stopStreaming = () => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
    setStreamingActive(false);
  };

  const runBulkAnalysis = async (type) => {
    try {
      setLoading(true);
      setError(null);

      const endpoint = `ai/enhanced/batch/${type}`;
      const response = await request.post({ entity: endpoint });

      if (response.success) {
        // Refresh metrics after bulk analysis
        await fetchPerformanceMetrics();
        
        // Show success message
        setStreamingData(prev => [...prev, {
          type: 'success',
          content: `Masowa analiza ${type} zakończona pomyślnie: ${response.result.summary?.successful || 0} pomyślnych`,
          timestamp: new Date()
        }]);
      }
    } catch (err) {
      console.error('Bulk analysis error:', err);
      setError(`Błąd podczas masowej analizy ${type}`);
    } finally {
      setLoading(false);
    }
  };

  const testAIFeature = async (testType) => {
    try {
      setLoading(true);
      setError(null);

      const response = await request.post({ 
        entity: 'ai/enhanced/test/function-calling',
        jsonData: { testType }
      });

      if (response.success) {
        setStreamingData(prev => [...prev, {
          type: 'test',
          content: `Test AI ${testType} zakończony pomyślnie`,
          timestamp: new Date(),
          details: response.result
        }]);
      }
    } catch (err) {
      console.error('AI test error:', err);
      setError(`Błąd podczas testowania AI ${testType}`);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'operational': return 'green';
      case 'configured': return 'blue';
      case 'not_configured': return 'orange';
      default: return 'gray';
    }
  };

  const getAdoptionColor = (percentage) => {
    if (percentage >= 80) return '#52c41a';
    if (percentage >= 60) return '#faad14';
    if (percentage >= 40) return '#ff7a45';
    return '#ff4d4f';
  };

  return (
    <div>
      {/* Header */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col span={24}>
          <Card>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <Title level={2} style={{ margin: 0, display: 'flex', alignItems: 'center' }}>
                  <ThunderboltOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
                  Enhanced AI Dashboard
                </Title>
                <Text type="secondary">
                  Zaawansowane funkcje AI z OpenAI Function Calling, Structured Output i Streaming
                </Text>
              </div>
              <Space>
                <Button 
                  icon={<ReloadOutlined />} 
                  onClick={() => {
                    fetchPerformanceMetrics();
                    fetchHealthStatus();
                  }}
                >
                  Odśwież
                </Button>
              </Space>
            </div>
          </Card>
        </Col>
      </Row>

      {error && (
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col span={24}>
            <Alert
              message="Błąd Enhanced AI"
              description={error}
              type="error"
              showIcon
              closable
              onClose={() => setError(null)}
            />
          </Col>
        </Row>
      )}

      <Tabs defaultActiveKey="overview" type="card">
        {/* Overview Tab */}
        <TabPane tab="Przegląd" key="overview">
          <Row gutter={[16, 16]}>
            {/* Health Status */}
            <Col xs={24} lg={12}>
              <Card title={
                <span>
                  <CheckCircleOutlined style={{ color: '#52c41a', marginRight: '8px' }} />
                  Status Systemu AI
                </span>
              }>
                {healthStatus ? (
                  <div>
                    <div style={{ marginBottom: '16px' }}>
                      <Tag color={getStatusColor(healthStatus.status)} style={{ marginBottom: '8px' }}>
                        {healthStatus.status.toUpperCase()}
                      </Tag>
                      <Tag color={getStatusColor(healthStatus.openaiConnection)}>
                        OpenAI: {healthStatus.openaiConnection}
                      </Tag>
                    </div>
                    
                    <Divider />
                    
                    <Title level={5}>Dostępne Funkcje:</Title>
                    <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
                      {Object.entries(healthStatus.features || {}).map(([feature, status]) => (
                        <Tag key={feature} color={getStatusColor(status)}>
                          {feature}
                        </Tag>
                      ))}
                    </div>

                    <Divider />

                    <Title level={5}>Możliwości AI:</Title>
                    <List
                      size="small"
                      dataSource={healthStatus.capabilities || []}
                      renderItem={(capability) => (
                        <List.Item>
                          <CheckCircleOutlined style={{ color: '#52c41a', marginRight: '8px' }} />
                          {capability}
                        </List.Item>
                      )}
                    />
                  </div>
                ) : (
                  <Spin />
                )}
              </Card>
            </Col>

            {/* Performance Metrics */}
            <Col xs={24} lg={12}>
              <Card title={
                <span>
                  <TrophyOutlined style={{ color: '#faad14', marginRight: '8px' }} />
                  Metryki Adopcji AI
                </span>
              }>
                {performanceMetrics ? (
                  <div>
                    <Row gutter={[16, 16]}>
                      <Col span={8}>
                        <Statistic
                          title="Sprzęt"
                          value={performanceMetrics.adoption.equipment.percentage}
                          suffix="%"
                          valueStyle={{ color: getAdoptionColor(performanceMetrics.adoption.equipment.percentage) }}
                        />
                        <Text type="secondary">
                          {performanceMetrics.adoption.equipment.withAI}/{performanceMetrics.adoption.equipment.total}
                        </Text>
                      </Col>
                      <Col span={8}>
                        <Statistic
                          title="Okazje"
                          value={performanceMetrics.adoption.opportunities.percentage}
                          suffix="%"
                          valueStyle={{ color: getAdoptionColor(performanceMetrics.adoption.opportunities.percentage) }}
                        />
                        <Text type="secondary">
                          {performanceMetrics.adoption.opportunities.withAI}/{performanceMetrics.adoption.opportunities.total}
                        </Text>
                      </Col>
                      <Col span={8}>
                        <Statistic
                          title="Klienci"
                          value={performanceMetrics.adoption.clients.percentage}
                          suffix="%"
                          valueStyle={{ color: getAdoptionColor(performanceMetrics.adoption.clients.percentage) }}
                        />
                        <Text type="secondary">
                          {performanceMetrics.adoption.clients.withAI}/{performanceMetrics.adoption.clients.total}
                        </Text>
                      </Col>
                    </Row>

                    <Divider />

                    <div style={{ textAlign: 'center' }}>
                      <Progress
                        type="circle"
                        percent={performanceMetrics.overall.overallAdoption}
                        strokeColor={getAdoptionColor(performanceMetrics.overall.overallAdoption)}
                        format={(percent) => `${percent}%`}
                      />
                      <div style={{ marginTop: '8px' }}>
                        <Text strong>Ogólna Adopcja AI</Text>
                      </div>
                    </div>
                  </div>
                ) : (
                  <Spin />
                )}
              </Card>
            </Col>
          </Row>
        </TabPane>

        {/* Streaming Tab */}
        <TabPane tab="Streaming AI" key="streaming">
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Card 
                title={
                  <span>
                    <RobotOutlined style={{ color: '#1890ff', marginRight: '8px' }} />
                    Real-time AI Insights
                  </span>
                }
                extra={
                  <Space>
                    <Button
                      type={streamingActive ? "default" : "primary"}
                      icon={streamingActive ? <StopOutlined /> : <PlayCircleOutlined />}
                      onClick={streamingActive ? stopStreaming : startStreaming}
                      loading={loading}
                    >
                      {streamingActive ? 'Zatrzymaj' : 'Rozpocznij'} Streaming
                    </Button>
                  </Space>
                }
              >
                <div style={{ height: '400px', overflowY: 'auto', border: '1px solid #f0f0f0', padding: '16px', borderRadius: '6px' }}>
                  {streamingData.length === 0 ? (
                    <div style={{ textAlign: 'center', color: '#666', marginTop: '100px' }}>
                      <RobotOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
                      <div>Kliknij "Rozpocznij Streaming" aby zobaczyć real-time AI insights</div>
                    </div>
                  ) : (
                    <List
                      dataSource={streamingData}
                      renderItem={(item, index) => (
                        <List.Item key={index}>
                          <div style={{ width: '100%' }}>
                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '4px' }}>
                              <Tag color={
                                item.type === 'connection' ? 'blue' :
                                item.type === 'insight' ? 'green' :
                                item.type === 'error' ? 'red' :
                                item.type === 'success' ? 'green' :
                                item.type === 'test' ? 'purple' :
                                'default'
                              }>
                                {item.type.toUpperCase()}
                              </Tag>
                              <Text type="secondary" style={{ fontSize: '12px' }}>
                                {new Date(item.timestamp).toLocaleTimeString('pl-PL')}
                              </Text>
                            </div>
                            <div>{item.content || item.message}</div>
                          </div>
                        </List.Item>
                      )}
                    />
                  )}
                </div>
              </Card>
            </Col>
          </Row>
        </TabPane>

        {/* Bulk Operations Tab */}
        <TabPane tab="Operacje Masowe" key="bulk">
          <Row gutter={[16, 16]}>
            <Col xs={24} md={8}>
              <Card 
                title="Diagnostyka Sprzętu"
                actions={[
                  <Button 
                    key="run"
                    type="primary" 
                    icon={<ToolOutlined />}
                    onClick={() => runBulkAnalysis('advanced-equipment-diagnostics')}
                    loading={loading}
                    block
                  >
                    Uruchom Analizę
                  </Button>
                ]}
              >
                <Paragraph>
                  Zaawansowana diagnostyka AI dla wszystkich urządzeń HVAC z wykorzystaniem 
                  OpenAI Function Calling i strukturalnych odpowiedzi.
                </Paragraph>
                <ul>
                  <li>Health Score Analysis</li>
                  <li>Failure Probability</li>
                  <li>Maintenance Recommendations</li>
                  <li>Cost Estimation</li>
                </ul>
              </Card>
            </Col>

            <Col xs={24} md={8}>
              <Card 
                title="Ocena Leadów"
                actions={[
                  <Button 
                    key="run"
                    type="primary" 
                    icon={<TrophyOutlined />}
                    onClick={() => runBulkAnalysis('intelligent-lead-scoring')}
                    loading={loading}
                    block
                  >
                    Uruchom Analizę
                  </Button>
                ]}
              >
                <Paragraph>
                  Inteligentna ocena wszystkich aktywnych leadów z wykorzystaniem 
                  zaawansowanych algorytmów AI i structured output.
                </Paragraph>
                <ul>
                  <li>Lead Score (0-100)</li>
                  <li>Conversion Probability</li>
                  <li>Priority Classification</li>
                  <li>Action Recommendations</li>
                </ul>
              </Card>
            </Col>

            <Col xs={24} md={8}>
              <Card 
                title="Analiza Klientów"
                actions={[
                  <Button 
                    key="run"
                    type="primary" 
                    icon={<HeartOutlined />}
                    onClick={() => runBulkAnalysis('comprehensive-customer-analysis')}
                    loading={loading}
                    block
                  >
                    Uruchom Analizę
                  </Button>
                ]}
              >
                <Paragraph>
                  Kompleksowa analiza wszystkich klientów z segmentacją, 
                  oceną ryzyka churn i strategiami retencji.
                </Paragraph>
                <ul>
                  <li>Customer Health Score</li>
                  <li>Churn Risk Assessment</li>
                  <li>Lifetime Value</li>
                  <li>Upsell Opportunities</li>
                </ul>
              </Card>
            </Col>
          </Row>
        </TabPane>

        {/* Testing Tab */}
        <TabPane tab="Testowanie AI" key="testing">
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Card title="Test Funkcji AI">
                <Space wrap>
                  <Button 
                    icon={<ToolOutlined />}
                    onClick={() => testAIFeature('equipment')}
                    loading={loading}
                  >
                    Test Diagnostyki Sprzętu
                  </Button>
                  <Button 
                    icon={<TrophyOutlined />}
                    onClick={() => testAIFeature('lead')}
                    loading={loading}
                  >
                    Test Oceny Leadów
                  </Button>
                  <Button 
                    icon={<HeartOutlined />}
                    onClick={() => testAIFeature('customer')}
                    loading={loading}
                  >
                    Test Analizy Klientów
                  </Button>
                </Space>
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default EnhancedAI;
